#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Écran de gestion des clients KivyMD
"""

from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.list import MDList, ThreeLineAvatarIconListItem
from kivymd.uix.list import IconLeftWidget, IconRightWidget
from kivymd.uix.textfield import MD<PERSON>ext<PERSON>ield
from kivymd.uix.button import MDIconButton
from kivymd.uix.card import MDCard
from kivymd.uix.label import MDLabel
# from kivymd.uix.refreshlayout import MDSwipeToRefresh  # Non disponible dans cette version

from kivy.metrics import dp

from .base_screen import BaseScreen
from ..models.client import Client

class ClientsScreen(BaseScreen):
    """Écran de gestion des clients avec interface moderne"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = "clients"
        self.clients_data = []
        self.filtered_clients = []
        self.build_screen()
        self.load_clients()
    
    def build_screen(self):
        """Construit l'écran des clients"""
        # Layout principal
        main_layout = MDBoxLayout(orientation="vertical")
        main_layout.spacing = dp(10)
        main_layout.padding = dp(10)
        
        # Barre d'outils
        toolbar = self.create_toolbar(
            "Gestion des Clients",
            actions=[
                {
                    'type': 'icon',
                    'icon': 'refresh',
                    'callback': self.refresh_data
                },
                {
                    'type': 'icon',
                    'icon': 'plus',
                    'callback': self.add_client
                }
            ]
        )
        main_layout.add_widget(toolbar)
        
        # Barre de recherche
        self.search_field = MDTextField(
            hint_text="Rechercher un client...",
            icon_left="magnify",
            size_hint_y=None,
            height=dp(56),
            on_text=self.on_search_text
        )
        main_layout.add_widget(self.search_field)
        
        # Statistiques rapides
        stats_card = self.create_stats_card()
        main_layout.add_widget(stats_card)
        
        # Liste des clients
        scroll = MDScrollView()
        self.clients_list = MDList()
        scroll.add_widget(self.clients_list)
        main_layout.add_widget(scroll)
        
        self.add_widget(main_layout)
    
    def create_stats_card(self):
        """Crée la carte des statistiques clients"""
        card = MDCard(
            size_hint_y=None,
            height=dp(80),
            elevation=2,
            padding=dp(15),
            md_bg_color="white",
            radius=[8]
        )
        
        layout = MDBoxLayout(orientation="horizontal", spacing=dp(20))
        
        # Nombre total de clients
        self.total_clients_label = MDLabel(
            text="Clients: 0",
            theme_text_color="Primary",
            font_style="Subtitle1",
            bold=True,
            size_hint_x=0.5
        )
        layout.add_widget(self.total_clients_label)
        
        # Clients actifs
        self.active_clients_label = MDLabel(
            text="Actifs: 0",
            theme_text_color="Primary", 
            font_style="Subtitle1",
            size_hint_x=0.5
        )
        layout.add_widget(self.active_clients_label)
        
        card.add_widget(layout)
        return card
    
    def load_clients(self):
        """Charge la liste des clients"""
        try:
            self.clients_data = Client.get_all()
            self.filtered_clients = self.clients_data.copy()
            self.update_clients_display()
            self.update_stats()
            
        except Exception as e:
            self.handle_error(e, "Chargement des clients")
            self.clients_data = []
            self.filtered_clients = []
    
    def update_clients_display(self):
        """Met à jour l'affichage de la liste des clients"""
        self.clients_list.clear_widgets()
        
        if not self.filtered_clients:
            # État vide
            empty_state = self.create_empty_state(
                "Aucun client trouvé\nAppuyez sur + pour ajouter un client",
                "account-plus"
            )
            self.clients_list.add_widget(empty_state)
            return
        
        for client in self.filtered_clients:
            # Créer l'item de liste
            item = self.create_client_item(client)
            self.clients_list.add_widget(item)
    
    def create_client_item(self, client):
        """Crée un item de liste pour un client"""
        # Texte principal
        primary_text = f"{client.prenom} {client.nom}"
        
        # Texte secondaire
        secondary_text = client.entreprise or "Particulier"
        
        # Texte tertiaire
        contact_info = []
        if client.email:
            contact_info.append(client.email)
        if client.telephone:
            contact_info.append(client.telephone)
        tertiary_text = " • ".join(contact_info) if contact_info else "Pas de contact"
        
        # Icône de gauche (avatar)
        left_icon = IconLeftWidget(
            icon="account-circle",
            theme_icon_color="Custom",
            icon_color="blue" if client.actif else "grey"
        )
        
        # Icône de droite (actions)
        right_icon = IconRightWidget(
            icon="chevron-right",
            theme_icon_color="Custom",
            icon_color="grey"
        )
        
        # Item de liste
        item = ThreeLineAvatarIconListItem(
            text=primary_text,
            secondary_text=secondary_text,
            tertiary_text=tertiary_text,
            left_widget=left_icon,
            right_widget=right_icon,
            on_release=lambda x, c=client: self.on_client_tap(c)
        )
        
        # Couleur de fond selon le statut
        if not client.actif:
            item.theme_text_color = "Disabled"
        
        return item
    
    def update_stats(self):
        """Met à jour les statistiques"""
        total = len(self.clients_data)
        actifs = len([c for c in self.clients_data if c.actif])
        
        self.total_clients_label.text = f"Clients: {total}"
        self.active_clients_label.text = f"Actifs: {actifs}"
    
    def on_search_text(self, instance, text):
        """Gère la recherche de clients"""
        if not text.strip():
            self.filtered_clients = self.clients_data.copy()
        else:
            search_term = text.lower().strip()
            self.filtered_clients = [
                client for client in self.clients_data
                if (search_term in client.nom.lower() or
                    search_term in client.prenom.lower() or
                    (client.entreprise and search_term in client.entreprise.lower()) or
                    (client.email and search_term in client.email.lower()))
            ]
        
        self.update_clients_display()
    
    def on_client_tap(self, client):
        """Gère le tap sur un client"""
        # Afficher les options pour le client
        self.show_client_options(client)
    
    def show_client_options(self, client):
        """Affiche les options pour un client"""
        from kivymd.uix.dialog import MDDialog
        from kivymd.uix.button import MDFlatButton
        from kivymd.uix.list import OneLineListItem
        
        # Créer les options
        options = [
            ("Voir détails", lambda: self.view_client_details(client)),
            ("Modifier", lambda: self.edit_client(client)),
            ("Voir commandes", lambda: self.view_client_orders(client)),
        ]
        
        if client.actif:
            options.append(("Désactiver", lambda: self.deactivate_client(client)))
        else:
            options.append(("Réactiver", lambda: self.activate_client(client)))
        
        # Créer la liste d'options
        options_list = MDList()
        for text, callback in options:
            item = OneLineListItem(
                text=text,
                on_release=lambda x, cb=callback: self.execute_option(cb)
            )
            options_list.add_widget(item)
        
        # Créer et afficher le dialog
        self.options_dialog = MDDialog(
            title=f"{client.prenom} {client.nom}",
            type="custom",
            content_cls=options_list,
            buttons=[
                MDFlatButton(
                    text="FERMER",
                    on_release=lambda x: self.options_dialog.dismiss()
                )
            ]
        )
        self.options_dialog.open()
    
    def execute_option(self, callback):
        """Exécute une option et ferme le dialog"""
        if hasattr(self, 'options_dialog'):
            self.options_dialog.dismiss()
        callback()
    
    def view_client_details(self, client):
        """Affiche les détails d'un client"""
        details_text = f"""
Nom: {client.prenom} {client.nom}
Entreprise: {client.entreprise or 'Particulier'}
Email: {client.email or 'Non renseigné'}
Téléphone: {client.telephone or 'Non renseigné'}
Adresse: {client.get_adresse_complete() or 'Non renseignée'}
Statut: {'Actif' if client.actif else 'Inactif'}
        """.strip()
        
        from kivymd.uix.dialog import MDDialog
        from kivymd.uix.button import MDFlatButton
        
        dialog = MDDialog(
            title="Détails du client",
            text=details_text,
            buttons=[
                MDFlatButton(
                    text="FERMER",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()
    
    def edit_client(self, client):
        """Modifie un client"""
        from .forms import ClientForm
        form = ClientForm(client=client, callback=self.refresh_data)
        form.show()
    
    def view_client_orders(self, client):
        """Affiche les commandes d'un client"""
        # TODO: Implémenter la vue des commandes
        self.show_snackbar(f"Commandes de {client.nom} en cours de développement")
    
    def deactivate_client(self, client):
        """Désactive un client"""
        def confirm_deactivate():
            try:
                client.delete()  # La méthode delete désactive le client
                self.refresh_data()
                self.show_success(f"Client {client.nom} désactivé")
            except Exception as e:
                self.handle_error(e, "Désactivation du client")
        
        self.show_confirmation_dialog(
            "Désactiver le client",
            f"Êtes-vous sûr de vouloir désactiver {client.prenom} {client.nom} ?",
            confirm_deactivate
        )
    
    def activate_client(self, client):
        """Réactive un client"""
        try:
            client.actif = True
            client.save()
            self.refresh_data()
            self.show_success(f"Client {client.nom} réactivé")
        except Exception as e:
            self.handle_error(e, "Réactivation du client")
    
    def add_client(self, *args):
        """Ajoute un nouveau client"""
        from .forms import ClientForm
        form = ClientForm(callback=self.refresh_data)
        form.show()
    
    def refresh_data(self, *args):
        """Rafraîchit les données des clients"""
        try:
            self.load_clients()
            self.show_success("Clients actualisés")
        except Exception as e:
            self.handle_error(e, "Actualisation")
        finally:
            pass
