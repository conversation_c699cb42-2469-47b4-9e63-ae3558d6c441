#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de test pour l'application KivyMD de gestion commerciale
"""

import sys
import os
from pathlib import Path

# Ajouter le répertoire src au path
sys.path.append(str(Path(__file__).parent / "src"))

def test_kivymd_imports():
    """Test des imports KivyMD"""
    print("Test des imports KivyMD...")
    try:
        import kivy
        print(f"✓ Kivy {kivy.__version__} disponible")
        
        import kivymd
        print(f"✓ KivyMD {kivymd.__version__} disponible")
        
        from kivymd.app import MDApp
        print("✓ MDApp importée")
        
        from kivymd.uix.screen import MDScreen
        print("✓ MDScreen importée")
        
        from kivymd.uix.boxlayout import MDBoxLayout
        print("✓ MDBoxLayout importée")
        
        return True
    except ImportError as e:
        print(f"✗ Erreur d'import KivyMD: {e}")
        return False
    except Exception as e:
        print(f"✗ Erreur: {e}")
        return False

def test_database():
    """Test de la base de données"""
    print("\nTest de la base de données...")
    try:
        from src.database.database import DatabaseManager
        db = DatabaseManager()
        print("✓ Base de données initialisée avec succès")
        return True
    except Exception as e:
        print(f"✗ Erreur base de données: {e}")
        return False

def test_models():
    """Test des modèles"""
    print("\nTest des modèles...")
    try:
        from src.models.client import Client
        from src.models.produit import Produit
        from src.models.commande import Commande
        
        # Test Client
        clients = Client.get_all()
        print(f"✓ {len(clients)} clients chargés")
        
        # Test Produit
        produits = Produit.get_all()
        print(f"✓ {len(produits)} produits chargés")
        
        # Test Commande
        commandes = Commande.get_all()
        print(f"✓ {len(commandes)} commandes chargées")
        
        return True
    except Exception as e:
        print(f"✗ Erreur modèles: {e}")
        return False

def test_kivymd_gui_imports():
    """Test des imports GUI KivyMD"""
    print("\nTest des imports GUI KivyMD...")
    try:
        from src.kivymd_gui.main_app import GestionCommercialeApp
        print("✓ GestionCommercialeApp importée")
        
        from src.kivymd_gui.dashboard_screen import DashboardScreen
        print("✓ DashboardScreen importée")
        
        from src.kivymd_gui.clients_screen import ClientsScreen
        print("✓ ClientsScreen importée")
        
        from src.kivymd_gui.produits_screen import ProduitsScreen
        print("✓ ProduitsScreen importée")
        
        from src.kivymd_gui.commandes_screen import CommandesScreen
        print("✓ CommandesScreen importée")
        
        return True
    except Exception as e:
        print(f"✗ Erreur GUI KivyMD: {e}")
        return False

def test_app_creation():
    """Test de création de l'application"""
    print("\nTest de création de l'application...")
    try:
        from src.kivymd_gui.main_app import GestionCommercialeApp
        
        # Créer l'application sans la lancer
        app = GestionCommercialeApp()
        print("✓ Application KivyMD créée avec succès")
        
        # Tester la configuration du thème
        print(f"✓ Thème principal: {app.theme_cls.primary_palette}")
        print(f"✓ Style: {app.theme_cls.theme_style}")
        
        return True
    except Exception as e:
        print(f"✗ Erreur création app: {e}")
        return False

def test_screen_creation():
    """Test de création des écrans"""
    print("\nTest de création des écrans...")
    try:
        from src.kivymd_gui.dashboard_screen import DashboardScreen
        from src.kivymd_gui.clients_screen import ClientsScreen
        from src.kivymd_gui.produits_screen import ProduitsScreen
        from src.kivymd_gui.commandes_screen import CommandesScreen
        
        # Créer les écrans
        dashboard = DashboardScreen()
        print("✓ DashboardScreen créé")
        
        clients = ClientsScreen()
        print("✓ ClientsScreen créé")
        
        produits = ProduitsScreen()
        print("✓ ProduitsScreen créé")
        
        commandes = CommandesScreen()
        print("✓ CommandesScreen créé")
        
        return True
    except Exception as e:
        print(f"✗ Erreur création écrans: {e}")
        return False

def check_dependencies():
    """Vérifie les dépendances"""
    print("\nVérification des dépendances...")
    
    dependencies = [
        ("kivy", "Interface graphique"),
        ("kivymd", "Material Design"),
        ("sqlite3", "Base de données"),
        ("datetime", "Gestion des dates"),
        ("pathlib", "Gestion des chemins")
    ]
    
    missing = []
    
    for dep, desc in dependencies:
        try:
            __import__(dep)
            print(f"✓ {dep} - {desc}")
        except ImportError:
            print(f"✗ {dep} - {desc} (MANQUANT)")
            missing.append(dep)
    
    if missing:
        print(f"\n⚠️  Dépendances manquantes: {', '.join(missing)}")
        print("Exécutez: install_kivymd.bat")
        return False
    
    return True

def main():
    """Fonction principale de test"""
    print("=== Test de l'Application KivyMD de Gestion Commerciale ===\n")
    
    tests = [
        check_dependencies,
        test_kivymd_imports,
        test_database,
        test_models,
        test_kivymd_gui_imports,
        test_app_creation,
        test_screen_creation
    ]
    
    results = []
    for test in tests:
        try:
            results.append(test())
        except Exception as e:
            print(f"✗ Erreur lors du test {test.__name__}: {e}")
            results.append(False)
    
    print("\n=== Résultats des Tests ===")
    passed = sum(results)
    total = len(results)
    
    print(f"Tests réussis: {passed}/{total}")
    
    if passed == total:
        print("✓ Tous les tests sont passés ! L'application KivyMD est prête.")
        print("\nVous pouvez maintenant lancer l'application avec:")
        print("  python main_kivymd.py")
        print("ou")
        print("  start_kivymd.bat")
        print("\nNote: L'application KivyMD nécessite un environnement graphique.")
    else:
        print("✗ Certains tests ont échoué. Vérifiez les erreurs ci-dessus.")
        
        if not results[0]:  # Dependencies check failed
            print("\n💡 Pour installer KivyMD:")
            print("  1. Exécutez: install_kivymd.bat")
            print("  2. Ou manuellement: pip install kivy kivymd")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🚀 Prêt pour le lancement !")
        print("L'application KivyMD offre une interface moderne Material Design.")
    else:
        print("\n🔧 Veuillez corriger les erreurs avant de continuer.")
    
    sys.exit(0 if success else 1)
