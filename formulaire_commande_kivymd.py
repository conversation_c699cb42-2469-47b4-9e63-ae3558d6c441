#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Formulaire de commande KivyMD
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent / "src"))

from kivymd.app import <PERSON><PERSON>pp
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.textfield import MDTextField
from kivymd.uix.button import MDRaisedButton, MDFlatButton, MDIconButton
from kivymd.uix.label import MDLabel
from kivymd.uix.card import MDCard
from kivymd.uix.dialog import MDDialog
from kivymd.uix.list import OneLineListItem, TwoLineListItem
from kivymd.uix.toolbar import MDTopAppBar
from kivymd.uix.menu import MDDropdownMenu

from kivy.metrics import dp
from kivy.clock import Clock
from datetime import datetime

# Imports des modèles
try:
    from src.models.client import Client
    from src.models.produit import Produit
    from src.models.commande import Commande
    from src.database.database import DatabaseManager
    MODELS_AVAILABLE = True
except ImportError as e:
    print(f"⚠ Modèles non disponibles: {e}")
    MODELS_AVAILABLE = False

class CommandeFormScreen(MDScreen):
    """Formulaire pour créer une commande"""
    
    def __init__(self, callback=None, **kwargs):
        super().__init__(**kwargs)
        self.name = "commande_form"
        self.callback = callback
        self.selected_client = None
        self.lignes_commande = []
        self.client_menu = None
        self.produit_menu = None
        self.build_form()
    
    def build_form(self):
        """Construit le formulaire commande"""
        # Layout principal
        main_layout = MDBoxLayout(orientation="vertical")
        
        # Barre de titre
        toolbar = MDTopAppBar(
            title="Nouvelle Commande",
            left_action_items=[["arrow-left", lambda x: self.go_back()]],
            right_action_items=[["content-save", lambda x: self.save_commande()]]
        )
        main_layout.add_widget(toolbar)
        
        # Contenu scrollable
        scroll = MDScrollView()
        content = MDBoxLayout(orientation="vertical", spacing=dp(15), padding=dp(20))
        content.bind(minimum_height=content.setter('height'))
        
        # Section client
        client_title = MDLabel(
            text="Sélection du client",
            font_style="H6",
            theme_text_color="Primary",
            size_hint_y=None,
            height=dp(40)
        )
        content.add_widget(client_title)
        
        # Bouton de sélection client
        self.client_button = MDRaisedButton(
            text="Sélectionner un client",
            on_release=self.show_client_selection,
            size_hint_y=None,
            height=dp(50)
        )
        content.add_widget(self.client_button)
        
        # Informations commande
        commande_title = MDLabel(
            text="Informations commande",
            font_style="H6",
            theme_text_color="Primary",
            size_hint_y=None,
            height=dp(40)
        )
        content.add_widget(commande_title)
        
        self.numero_field = MDTextField(
            hint_text="Numéro de commande",
            helper_text="Généré automatiquement si vide",
            helper_text_mode="on_focus"
        )
        content.add_widget(self.numero_field)
        
        # Section produits
        produits_title = MDLabel(
            text="Produits de la commande",
            font_style="H6",
            theme_text_color="Primary",
            size_hint_y=None,
            height=dp(40)
        )
        content.add_widget(produits_title)
        
        # Bouton d'ajout de produit
        add_produit_btn = MDRaisedButton(
            text="Ajouter un produit",
            on_release=self.show_produit_selection,
            size_hint_y=None,
            height=dp(50)
        )
        content.add_widget(add_produit_btn)
        
        # Liste des produits
        self.produits_card = MDCard(
            size_hint_y=None,
            height=dp(200),
            elevation=2,
            padding=dp(10)
        )
        
        self.produits_layout = MDBoxLayout(
            orientation="vertical",
            spacing=dp(5)
        )
        
        produits_scroll = MDScrollView()
        produits_scroll.add_widget(self.produits_layout)
        self.produits_card.add_widget(produits_scroll)
        
        content.add_widget(self.produits_card)
        
        # Totaux
        totaux_title = MDLabel(
            text="Totaux",
            font_style="H6",
            theme_text_color="Primary",
            size_hint_y=None,
            height=dp(40)
        )
        content.add_widget(totaux_title)
        
        self.total_ht_label = MDLabel(
            text="Total HT: 0.00 €",
            theme_text_color="Primary",
            font_style="Subtitle1"
        )
        content.add_widget(self.total_ht_label)
        
        self.total_tva_label = MDLabel(
            text="Total TVA: 0.00 €",
            theme_text_color="Primary",
            font_style="Subtitle1"
        )
        content.add_widget(self.total_tva_label)
        
        self.total_ttc_label = MDLabel(
            text="Total TTC: 0.00 €",
            theme_text_color="Primary",
            font_style="H6",
            bold=True
        )
        content.add_widget(self.total_ttc_label)
        
        # Boutons d'action
        buttons_layout = MDBoxLayout(
            orientation="horizontal",
            spacing=dp(10),
            size_hint_y=None,
            height=dp(60),
            padding=[0, dp(20), 0, 0]
        )
        
        cancel_btn = MDFlatButton(
            text="Annuler",
            on_release=self.go_back
        )
        buttons_layout.add_widget(cancel_btn)
        
        save_btn = MDRaisedButton(
            text="Enregistrer",
            on_release=self.save_commande
        )
        buttons_layout.add_widget(save_btn)
        
        content.add_widget(buttons_layout)
        
        # Initialiser l'affichage
        self.update_produits_display()
        self.calculate_totals()
        
        scroll.add_widget(content)
        main_layout.add_widget(scroll)
        self.add_widget(main_layout)
    
    def show_client_selection(self, *args):
        """Affiche la sélection de client"""
        if MODELS_AVAILABLE:
            try:
                clients = Client.get_all()
                if clients:
                    # Créer un dialog avec liste de clients
                    items = []
                    for client in clients:
                        if getattr(client, 'actif', True):
                            item_text = f"{getattr(client, 'prenom', '')} {getattr(client, 'nom', '')}"
                            if getattr(client, 'entreprise', None):
                                item_text += f" ({client.entreprise})"
                            items.append(
                                OneLineListItem(
                                    text=item_text,
                                    on_release=lambda x, c=client: self.select_client(c)
                                )
                            )
                    
                    if items:
                        self.show_selection_dialog("Sélectionner un client", items)
                    else:
                        self.show_error("Aucun client actif trouvé")
                else:
                    self.show_error("Aucun client trouvé")
            except Exception as e:
                self.show_error(f"Erreur lors du chargement des clients: {e}")
        else:
            # Mode simulation
            self.simulate_client_selection()
    
    def simulate_client_selection(self):
        """Simule la sélection d'un client"""
        class FakeClient:
            def __init__(self, prenom, nom, entreprise=None):
                self.prenom = prenom
                self.nom = nom
                self.entreprise = entreprise
                self.id = 1
        
        fake_client = FakeClient("Jean", "Dupont", "ACME Corp")
        self.select_client(fake_client)
    
    def show_produit_selection(self, *args):
        """Affiche la sélection de produit"""
        if MODELS_AVAILABLE:
            try:
                produits = Produit.get_all()
                if produits:
                    items = []
                    for produit in produits:
                        if getattr(produit, 'actif', True) and getattr(produit, 'stock_actuel', 0) > 0:
                            item_text = f"{getattr(produit, 'nom', '')} - {getattr(produit, 'prix_unitaire', 0):.2f}€"
                            items.append(
                                OneLineListItem(
                                    text=item_text,
                                    on_release=lambda x, p=produit: self.add_produit_to_commande(p)
                                )
                            )
                    
                    if items:
                        self.show_selection_dialog("Sélectionner un produit", items)
                    else:
                        self.show_error("Aucun produit disponible")
                else:
                    self.show_error("Aucun produit trouvé")
            except Exception as e:
                self.show_error(f"Erreur lors du chargement des produits: {e}")
        else:
            # Mode simulation
            self.simulate_produit_selection()
    
    def simulate_produit_selection(self):
        """Simule la sélection d'un produit"""
        class FakeProduit:
            def __init__(self, nom, prix, stock=10):
                self.nom = nom
                self.prix_unitaire = prix
                self.stock_actuel = stock
                self.taux_tva = 20.0
                self.id = 1
        
        fake_produit = FakeProduit("Produit Test", 25.99)
        self.add_produit_to_commande(fake_produit)
    
    def show_selection_dialog(self, title, items):
        """Affiche un dialog de sélection"""
        dialog_content = MDBoxLayout(orientation="vertical", spacing=dp(10))
        
        for item in items:
            dialog_content.add_widget(item)
        
        dialog = MDDialog(
            title=title,
            type="custom",
            content_cls=dialog_content,
            buttons=[
                MDFlatButton(
                    text="Annuler",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()
        
        # Stocker la référence pour pouvoir la fermer
        self.current_dialog = dialog
    
    def select_client(self, client):
        """Sélectionne un client"""
        self.selected_client = client
        client_text = f"{getattr(client, 'prenom', '')} {getattr(client, 'nom', '')}"
        if getattr(client, 'entreprise', None):
            client_text += f" ({client.entreprise})"
        self.client_button.text = client_text
        
        # Fermer le dialog
        if hasattr(self, 'current_dialog'):
            self.current_dialog.dismiss()
        
        print(f"✅ Client sélectionné: {client_text}")
    
    def add_produit_to_commande(self, produit):
        """Ajoute un produit à la commande"""
        # Vérifier si le produit est déjà dans la commande
        for ligne in self.lignes_commande:
            if ligne['produit'].id == produit.id:
                ligne['quantite'] += 1
                self.update_produits_display()
                self.calculate_totals()
                if hasattr(self, 'current_dialog'):
                    self.current_dialog.dismiss()
                return
        
        # Ajouter nouveau produit
        ligne = {
            'produit': produit,
            'quantite': 1,
            'prix_unitaire': getattr(produit, 'prix_unitaire', 0)
        }
        self.lignes_commande.append(ligne)
        self.update_produits_display()
        self.calculate_totals()
        
        # Fermer le dialog
        if hasattr(self, 'current_dialog'):
            self.current_dialog.dismiss()
        
        print(f"✅ Produit ajouté: {getattr(produit, 'nom', '')}")

    def update_produits_display(self):
        """Met à jour l'affichage des produits"""
        self.produits_layout.clear_widgets()

        if not self.lignes_commande:
            no_products = MDLabel(
                text="Aucun produit ajouté",
                theme_text_color="Secondary",
                halign="center"
            )
            self.produits_layout.add_widget(no_products)
            return

        for i, ligne in enumerate(self.lignes_commande):
            produit = ligne['produit']
            quantite = ligne['quantite']
            prix_unitaire = ligne['prix_unitaire']
            total_ligne = quantite * prix_unitaire

            # Layout pour chaque ligne
            ligne_layout = MDBoxLayout(
                orientation="horizontal",
                spacing=dp(10),
                size_hint_y=None,
                height=dp(60),
                padding=[dp(5), dp(5), dp(5), dp(5)]
            )

            # Informations produit
            info_layout = MDBoxLayout(
                orientation="vertical",
                size_hint_x=0.6
            )

            nom_label = MDLabel(
                text=getattr(produit, 'nom', 'Produit'),
                font_style="Subtitle1",
                theme_text_color="Primary"
            )
            info_layout.add_widget(nom_label)

            prix_label = MDLabel(
                text=f"{prix_unitaire:.2f}€ x {quantite} = {total_ligne:.2f}€",
                font_style="Caption",
                theme_text_color="Secondary"
            )
            info_layout.add_widget(prix_label)

            ligne_layout.add_widget(info_layout)

            # Contrôles quantité
            qty_layout = MDBoxLayout(
                orientation="horizontal",
                spacing=dp(5),
                size_hint_x=0.3
            )

            minus_btn = MDIconButton(
                icon="minus",
                size_hint=(None, None),
                size=(dp(30), dp(30)),
                on_release=lambda x, idx=i: self.decrease_quantity(idx)
            )
            qty_layout.add_widget(minus_btn)

            qty_label = MDLabel(
                text=str(quantite),
                halign="center",
                theme_text_color="Primary"
            )
            qty_layout.add_widget(qty_label)

            plus_btn = MDIconButton(
                icon="plus",
                size_hint=(None, None),
                size=(dp(30), dp(30)),
                on_release=lambda x, idx=i: self.increase_quantity(idx)
            )
            qty_layout.add_widget(plus_btn)

            ligne_layout.add_widget(qty_layout)

            # Bouton supprimer
            delete_btn = MDIconButton(
                icon="delete",
                size_hint_x=0.1,
                on_release=lambda x, idx=i: self.remove_ligne(idx)
            )
            ligne_layout.add_widget(delete_btn)

            self.produits_layout.add_widget(ligne_layout)

    def increase_quantity(self, index):
        """Augmente la quantité d'un produit"""
        if index < len(self.lignes_commande):
            produit = self.lignes_commande[index]['produit']
            stock_actuel = getattr(produit, 'stock_actuel', 999)
            if self.lignes_commande[index]['quantite'] < stock_actuel:
                self.lignes_commande[index]['quantite'] += 1
                self.update_produits_display()
                self.calculate_totals()
            else:
                self.show_error(f"Stock insuffisant pour {getattr(produit, 'nom', 'ce produit')}")

    def decrease_quantity(self, index):
        """Diminue la quantité d'un produit"""
        if index < len(self.lignes_commande):
            if self.lignes_commande[index]['quantite'] > 1:
                self.lignes_commande[index]['quantite'] -= 1
                self.update_produits_display()
                self.calculate_totals()
            else:
                self.remove_ligne(index)

    def remove_ligne(self, index):
        """Supprime une ligne de commande"""
        if index < len(self.lignes_commande):
            produit_nom = getattr(self.lignes_commande[index]['produit'], 'nom', 'Produit')
            self.lignes_commande.pop(index)
            self.update_produits_display()
            self.calculate_totals()
            print(f"🗑️ Produit supprimé: {produit_nom}")

    def calculate_totals(self):
        """Calcule les totaux de la commande"""
        total_ht = 0
        total_tva = 0

        for ligne in self.lignes_commande:
            produit = ligne['produit']
            quantite = ligne['quantite']
            prix_unitaire = ligne['prix_unitaire']

            ligne_ht = quantite * prix_unitaire
            taux_tva = getattr(produit, 'taux_tva', 20.0)
            ligne_tva = ligne_ht * (taux_tva / 100)

            total_ht += ligne_ht
            total_tva += ligne_tva

        total_ttc = total_ht + total_tva

        self.total_ht_label.text = f"Total HT: {total_ht:.2f} €"
        self.total_tva_label.text = f"Total TVA: {total_tva:.2f} €"
        self.total_ttc_label.text = f"Total TTC: {total_ttc:.2f} €"

    def validate_form(self):
        """Valide le formulaire"""
        errors = []

        if not self.selected_client:
            errors.append("Veuillez sélectionner un client")

        if not self.lignes_commande:
            errors.append("Veuillez ajouter au moins un produit")

        return errors

    def save_commande(self, *args):
        """Sauvegarde la commande"""
        print("💾 Tentative de sauvegarde de la commande...")

        # Valider
        errors = self.validate_form()
        if errors:
            self.show_error("\n".join(errors))
            return

        try:
            # Calculer les totaux
            total_ht = sum(ligne['quantite'] * ligne['prix_unitaire'] for ligne in self.lignes_commande)
            total_tva = sum(ligne['quantite'] * ligne['prix_unitaire'] * (getattr(ligne['produit'], 'taux_tva', 20.0) / 100)
                           for ligne in self.lignes_commande)
            total_ttc = total_ht + total_tva

            if MODELS_AVAILABLE:
                # Création avec vrais modèles
                commande_data = {
                    'client_id': self.selected_client.id,
                    'numero_commande': self.numero_field.text.strip() or None,
                    'statut': 'En attente',
                    'total_ht': total_ht,
                    'total_tva': total_tva,
                    'total_ttc': total_ttc,
                    'date_commande': datetime.now()
                }
                commande = Commande.create(**commande_data)

                # Créer les lignes de commande (si le modèle existe)
                # TODO: Implémenter LigneCommande si nécessaire

                message = f"Commande créée avec succès - Total: {total_ttc:.2f}€"
            else:
                # Mode simulation
                client_nom = f"{getattr(self.selected_client, 'prenom', '')} {getattr(self.selected_client, 'nom', '')}"
                message = f"✅ Commande simulée pour {client_nom} - Total: {total_ttc:.2f}€"
                print(f"📝 Commande: {len(self.lignes_commande)} produits - {total_ttc:.2f}€")

            # Callback et retour
            if self.callback:
                self.callback()

            self.show_success(message)
            Clock.schedule_once(lambda dt: self.go_back(), 2.0)

        except Exception as e:
            self.show_error(f"Erreur lors de la sauvegarde: {e}")

    def show_error(self, message):
        """Affiche un message d'erreur"""
        dialog = MDDialog(
            title="Erreur",
            text=message,
            buttons=[
                MDFlatButton(
                    text="OK",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()

    def show_success(self, message):
        """Affiche un message de succès"""
        dialog = MDDialog(
            title="Succès",
            text=message,
            buttons=[
                MDFlatButton(
                    text="OK",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()

    def go_back(self, *args):
        """Retour à l'écran précédent"""
        print("🔙 Retour depuis le formulaire commande")
        if self.callback:
            self.callback()
        # Navigation vers l'écran dashboard
        app = MDApp.get_running_app()
        if hasattr(app, 'screen_manager'):
            app.screen_manager.current = "dashboard"
