#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Écran de gestion des produits KivyMD
"""

from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.list import MDList, ThreeLineAvatarIconListItem
from kivymd.uix.list import IconLeftWidget, IconRightWidget
from kivymd.uix.textfield import MDText<PERSON>ield
from kivymd.uix.button import MDIconButton
from kivymd.uix.chip import MDChip
from kivymd.uix.card import MDCard
from kivymd.uix.label import MDLabel
# from kivymd.uix.refreshlayout import MDSwipeToRefresh  # Non disponible dans cette version
from kivymd.uix.gridlayout import MDGridLayout

from kivy.metrics import dp

from .base_screen import BaseScreen
from ..models.produit import Produit

class ProduitsScreen(BaseScreen):
    """Écran de gestion des produits avec interface moderne"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = "produits"
        self.produits_data = []
        self.filtered_produits = []
        self.filter_rupture = False
        self.build_screen()
        self.load_produits()
    
    def build_screen(self):
        """Construit l'écran des produits"""
        # Layout principal
        main_layout = MDBoxLayout(orientation="vertical")
        main_layout.spacing = dp(10)
        main_layout.padding = dp(10)
        
        # Barre d'outils
        toolbar = self.create_toolbar(
            "Gestion des Produits",
            actions=[
                {
                    'type': 'icon',
                    'icon': 'refresh',
                    'callback': self.refresh_data
                },
                {
                    'type': 'icon',
                    'icon': 'plus',
                    'callback': self.add_produit
                }
            ]
        )
        main_layout.add_widget(toolbar)
        
        # Barre de recherche
        self.search_field = MDTextField(
            hint_text="Rechercher un produit...",
            icon_left="magnify",
            size_hint_y=None,
            height=dp(56),
            on_text=self.on_search_text
        )
        main_layout.add_widget(self.search_field)
        
        # Filtres
        filters_layout = self.create_filters()
        main_layout.add_widget(filters_layout)
        
        # Statistiques rapides
        stats_card = self.create_stats_card()
        main_layout.add_widget(stats_card)
        
        # Liste des produits
        scroll = MDScrollView()
        self.produits_list = MDList()
        scroll.add_widget(self.produits_list)
        main_layout.add_widget(scroll)
        
        self.add_widget(main_layout)
    
    def create_filters(self):
        """Crée la section des filtres"""
        layout = MDBoxLayout(
            orientation="horizontal",
            size_hint_y=None,
            height=dp(50),
            spacing=dp(10)
        )
        
        # Chip pour filtrer les ruptures
        self.rupture_chip = MDChip(
            text="Ruptures de stock",
            icon="alert-circle",
            check=False,
            on_release=self.toggle_rupture_filter
        )
        layout.add_widget(self.rupture_chip)
        
        return layout
    
    def create_stats_card(self):
        """Crée la carte des statistiques produits"""
        card = MDCard(
            size_hint_y=None,
            height=dp(100),
            elevation=2,
            padding=dp(15),
            md_bg_color="white",
            radius=[8]
        )
        
        layout = MDGridLayout(cols=2, spacing=dp(10))
        
        # Nombre total de produits
        self.total_produits_label = MDLabel(
            text="Produits: 0",
            theme_text_color="Primary",
            font_style="Subtitle1",
            bold=True
        )
        layout.add_widget(self.total_produits_label)
        
        # Produits en rupture
        self.rupture_produits_label = MDLabel(
            text="Ruptures: 0",
            theme_text_color="Error",
            font_style="Subtitle1",
            bold=True
        )
        layout.add_widget(self.rupture_produits_label)
        
        # Valeur du stock
        self.valeur_stock_label = MDLabel(
            text="Valeur: 0€",
            theme_text_color="Primary",
            font_style="Subtitle1"
        )
        layout.add_widget(self.valeur_stock_label)
        
        # Stock total
        self.stock_total_label = MDLabel(
            text="Stock: 0",
            theme_text_color="Primary",
            font_style="Subtitle1"
        )
        layout.add_widget(self.stock_total_label)
        
        card.add_widget(layout)
        return card
    
    def load_produits(self):
        """Charge la liste des produits"""
        try:
            self.produits_data = Produit.get_all()
            self.apply_filters()
            self.update_stats()
            
        except Exception as e:
            self.handle_error(e, "Chargement des produits")
            self.produits_data = []
            self.filtered_produits = []
    
    def apply_filters(self):
        """Applique les filtres actifs"""
        self.filtered_produits = self.produits_data.copy()
        
        # Filtre de recherche
        search_text = self.search_field.text.lower().strip() if hasattr(self, 'search_field') else ""
        if search_text:
            self.filtered_produits = [
                produit for produit in self.filtered_produits
                if (search_text in produit.nom.lower() or
                    (produit.code_produit and search_text in produit.code_produit.lower()) or
                    (produit.description and search_text in produit.description.lower()))
            ]
        
        # Filtre rupture de stock
        if self.filter_rupture:
            self.filtered_produits = [
                produit for produit in self.filtered_produits
                if produit.est_en_rupture()
            ]
        
        self.update_produits_display()
    
    def update_produits_display(self):
        """Met à jour l'affichage de la liste des produits"""
        self.produits_list.clear_widgets()
        
        if not self.filtered_produits:
            # État vide
            message = "Aucun produit en rupture" if self.filter_rupture else "Aucun produit trouvé"
            empty_state = self.create_empty_state(
                f"{message}\nAppuyez sur + pour ajouter un produit",
                "package-variant-plus"
            )
            self.produits_list.add_widget(empty_state)
            return
        
        for produit in self.filtered_produits:
            # Créer l'item de liste
            item = self.create_produit_item(produit)
            self.produits_list.add_widget(item)
    
    def create_produit_item(self, produit):
        """Crée un item de liste pour un produit"""
        # Texte principal
        primary_text = produit.nom
        
        # Texte secondaire
        secondary_text = f"{produit.prix_unitaire:.2f}€"
        if produit.code_produit:
            secondary_text += f" • {produit.code_produit}"
        
        # Texte tertiaire avec stock
        if produit.est_en_rupture():
            tertiary_text = f"⚠️ RUPTURE - Stock: {produit.stock_actuel} (Min: {produit.stock_minimum})"
            text_color = "Error"
            icon_color = "red"
        elif produit.stock_actuel <= produit.stock_minimum * 1.5:
            tertiary_text = f"⚡ Stock faible: {produit.stock_actuel} (Min: {produit.stock_minimum})"
            text_color = "Warning"
            icon_color = "orange"
        else:
            tertiary_text = f"✅ Stock: {produit.stock_actuel} (Min: {produit.stock_minimum})"
            text_color = "Primary"
            icon_color = "green"
        
        # Icône de gauche
        left_icon = IconLeftWidget(
            icon="package-variant",
            theme_icon_color="Custom",
            icon_color=icon_color
        )
        
        # Icône de droite
        right_icon = IconRightWidget(
            icon="chevron-right",
            theme_icon_color="Custom",
            icon_color="grey"
        )
        
        # Item de liste
        item = ThreeLineAvatarIconListItem(
            text=primary_text,
            secondary_text=secondary_text,
            tertiary_text=tertiary_text,
            left_widget=left_icon,
            right_widget=right_icon,
            theme_text_color=text_color,
            on_release=lambda x, p=produit: self.on_produit_tap(p)
        )
        
        return item
    
    def update_stats(self):
        """Met à jour les statistiques"""
        total = len(self.produits_data)
        ruptures = len([p for p in self.produits_data if p.est_en_rupture()])
        valeur_stock = sum(p.stock_actuel * p.prix_unitaire for p in self.produits_data)
        stock_total = sum(p.stock_actuel for p in self.produits_data)
        
        self.total_produits_label.text = f"Produits: {total}"
        self.rupture_produits_label.text = f"Ruptures: {ruptures}"
        self.valeur_stock_label.text = f"Valeur: {valeur_stock:.2f}€"
        self.stock_total_label.text = f"Stock: {stock_total}"
    
    def toggle_rupture_filter(self, chip):
        """Bascule le filtre des ruptures de stock"""
        self.filter_rupture = chip.check
        self.apply_filters()
    
    def on_search_text(self, instance, text):
        """Gère la recherche de produits"""
        self.apply_filters()
    
    def on_produit_tap(self, produit):
        """Gère le tap sur un produit"""
        self.show_produit_options(produit)
    
    def show_produit_options(self, produit):
        """Affiche les options pour un produit"""
        from kivymd.uix.dialog import MDDialog
        from kivymd.uix.button import MDFlatButton
        from kivymd.uix.list import OneLineListItem
        
        # Créer les options
        options = [
            ("Voir détails", lambda: self.view_produit_details(produit)),
            ("Modifier", lambda: self.edit_produit(produit)),
            ("Ajuster stock", lambda: self.adjust_stock(produit)),
        ]
        
        if produit.actif:
            options.append(("Désactiver", lambda: self.deactivate_produit(produit)))
        else:
            options.append(("Réactiver", lambda: self.activate_produit(produit)))
        
        # Créer la liste d'options
        options_list = MDList()
        for text, callback in options:
            item = OneLineListItem(
                text=text,
                on_release=lambda x, cb=callback: self.execute_option(cb)
            )
            options_list.add_widget(item)
        
        # Créer et afficher le dialog
        self.options_dialog = MDDialog(
            title=produit.nom,
            type="custom",
            content_cls=options_list,
            buttons=[
                MDFlatButton(
                    text="FERMER",
                    on_release=lambda x: self.options_dialog.dismiss()
                )
            ]
        )
        self.options_dialog.open()
    
    def execute_option(self, callback):
        """Exécute une option et ferme le dialog"""
        if hasattr(self, 'options_dialog'):
            self.options_dialog.dismiss()
        callback()
    
    def view_produit_details(self, produit):
        """Affiche les détails d'un produit"""
        details_text = f"""
Nom: {produit.nom}
Code: {produit.code_produit or 'Non défini'}
Prix: {produit.prix_unitaire:.2f}€
Stock actuel: {produit.stock_actuel}
Stock minimum: {produit.stock_minimum}
Description: {produit.description or 'Aucune'}
Statut: {'Actif' if produit.actif else 'Inactif'}
        """.strip()
        
        from kivymd.uix.dialog import MDDialog
        from kivymd.uix.button import MDFlatButton
        
        dialog = MDDialog(
            title="Détails du produit",
            text=details_text,
            buttons=[
                MDFlatButton(
                    text="FERMER",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()
    
    def edit_produit(self, produit):
        """Modifie un produit"""
        from .forms import ProduitForm
        form = ProduitForm(produit=produit, callback=self.refresh_data)
        form.show()
    
    def adjust_stock(self, produit):
        """Ajuste le stock d'un produit"""
        # TODO: Implémenter l'ajustement de stock
        self.show_snackbar(f"Ajustement de stock pour {produit.nom} en cours de développement")
    
    def deactivate_produit(self, produit):
        """Désactive un produit"""
        def confirm_deactivate():
            try:
                produit.delete()
                self.refresh_data()
                self.show_success(f"Produit {produit.nom} désactivé")
            except Exception as e:
                self.handle_error(e, "Désactivation du produit")
        
        self.show_confirmation_dialog(
            "Désactiver le produit",
            f"Êtes-vous sûr de vouloir désactiver {produit.nom} ?",
            confirm_deactivate
        )
    
    def activate_produit(self, produit):
        """Réactive un produit"""
        try:
            produit.actif = True
            produit.save()
            self.refresh_data()
            self.show_success(f"Produit {produit.nom} réactivé")
        except Exception as e:
            self.handle_error(e, "Réactivation du produit")
    
    def add_produit(self, *args):
        """Ajoute un nouveau produit"""
        from .forms import ProduitForm
        form = ProduitForm(callback=self.refresh_data)
        form.show()
    
    def refresh_data(self, *args):
        """Rafraîchit les données des produits"""
        try:
            self.load_produits()
            self.show_success("Produits actualisés")
        except Exception as e:
            self.handle_error(e, "Actualisation")
        finally:
            pass
