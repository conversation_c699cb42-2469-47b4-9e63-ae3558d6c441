#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Application de Gestion Commerciale - Version KivyMD Simple
Version simplifiée qui fonctionne avec KivyMD 1.2.0
"""

import sys
import os
from pathlib import Path

# Ajouter le répertoire src au path
sys.path.append(str(Path(__file__).parent / "src"))

from kivymd.app import MDApp
from kivymd.uix.screenmanager import MDScreenManager
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.toolbar import MDTopAppBar
from kivymd.uix.bottomnavigation import MDBottomNavigation, MDBottomNavigationItem
from kivymd.uix.label import MDLabel
from kivymd.uix.card import MDCard
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.list import MDList, OneLineListItem, TwoLineListItem, ThreeLineListItem
from kivymd.uix.button import MDR<PERSON>edButton, MDIconButton, MDFlatButton
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.dialog import MDDialog

from kivy.metrics import dp

# Imports des modèles
from src.database.database import DatabaseManager
from src.models.client import Client
from src.models.produit import Produit
from src.models.commande import Commande

class DashboardScreen(MDScreen):
    """Écran du tableau de bord simplifié"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = "dashboard"
        self.build_screen()
    
    def build_screen(self):
        """Construit l'écran du tableau de bord"""
        scroll = MDScrollView()
        layout = MDBoxLayout(orientation="vertical", spacing=dp(10), padding=dp(10))
        
        # Titre et bouton de rafraîchissement
        header_layout = MDBoxLayout(orientation="horizontal", size_hint_y=None, height=dp(60))

        title = MDLabel(
            text="Tableau de Bord",
            theme_text_color="Primary",
            font_style="H4",
            halign="center"
        )
        header_layout.add_widget(title)

        refresh_btn = MDIconButton(
            icon="refresh",
            size_hint=(None, None),
            size=(dp(40), dp(40)),
            on_release=self.refresh_dashboard
        )
        header_layout.add_widget(refresh_btn)

        layout.add_widget(header_layout)
        
        # Grille pour les statistiques
        stats_grid = MDGridLayout(cols=2, spacing=dp(10), size_hint_y=None, height=dp(200))
        
        try:
            db = DatabaseManager()
            
            # Statistiques
            stats = [
                ("Clients", "SELECT COUNT(*) FROM clients WHERE actif=1"),
                ("Produits", "SELECT COUNT(*) FROM produits WHERE actif=1"),
                ("Commandes", "SELECT COUNT(*) FROM commandes WHERE statut='En attente'"),
                ("CA Mois", "SELECT COALESCE(SUM(total_ttc), 0) FROM commandes WHERE date_commande >= date('now', 'start of month')")
            ]
            
            for title_stat, query in stats:
                result = db.execute_query(query)
                value = result[0][0] if result else 0
                
                if title_stat == "CA Mois":
                    value_text = f"{value:.2f}€"
                else:
                    value_text = str(value)
                
                card = self.create_stat_card(title_stat, value_text)
                stats_grid.add_widget(card)
                
        except Exception as e:
            error_label = MDLabel(text=f"Erreur: {e}", theme_text_color="Error")
            stats_grid.add_widget(error_label)
        
        layout.add_widget(stats_grid)
        
        # Alertes de stock
        alerts_card = self.create_alerts_section()
        layout.add_widget(alerts_card)
        
        scroll.add_widget(layout)
        self.add_widget(scroll)
    
    def create_stat_card(self, title, value):
        """Crée une carte de statistique cliquable"""
        card = MDCard(
            size_hint_y=None,
            height=dp(80),
            elevation=2,
            padding=dp(10),
            md_bg_color=(1, 1, 1, 1),
            on_release=lambda x: self.on_stat_card_click(title, value)
        )
        
        layout = MDBoxLayout(orientation="vertical")
        
        title_label = MDLabel(
            text=title,
            theme_text_color="Primary",
            font_style="Subtitle1",
            size_hint_y=None,
            height=dp(30),
            halign="center"
        )
        layout.add_widget(title_label)
        
        value_label = MDLabel(
            text=value,
            theme_text_color="Primary",
            font_style="H6",
            bold=True,
            halign="center"
        )
        layout.add_widget(value_label)
        
        card.add_widget(layout)
        return card
    
    def create_alerts_section(self):
        """Crée la section des alertes"""
        card = MDCard(
            size_hint_y=None,
            height=dp(200),
            elevation=2,
            padding=dp(10),
            md_bg_color=(1, 1, 1, 1)
        )
        
        layout = MDBoxLayout(orientation="vertical")
        
        title = MDLabel(
            text="Alertes Stock",
            theme_text_color="Primary",
            font_style="H6",
            size_hint_y=None,
            height=dp(30),
            halign="center"
        )
        layout.add_widget(title)
        
        alerts_list = MDList()
        
        try:
            db = DatabaseManager()
            query = """SELECT nom, stock_actuel, stock_minimum 
                      FROM produits 
                      WHERE stock_actuel <= stock_minimum AND actif=1 
                      ORDER BY nom LIMIT 5"""
            results = db.execute_query(query)
            
            if results:
                for row in results:
                    item = TwoLineListItem(
                        text=row[0],
                        secondary_text=f"Stock: {row[1]} (Min: {row[2]})"
                    )
                    alerts_list.add_widget(item)
            else:
                item = OneLineListItem(text="Aucune alerte de stock")
                alerts_list.add_widget(item)
                
        except Exception as e:
            item = OneLineListItem(text=f"Erreur: {e}")
            alerts_list.add_widget(item)
        
        layout.add_widget(alerts_list)
        card.add_widget(layout)

        return card

    def refresh_dashboard(self, *args):
        """Rafraîchit le tableau de bord"""
        print("Bouton 'Rafraîchir' cliqué !")
        # Reconstruire l'écran
        self.clear_widgets()
        self.build_screen()
        print("Tableau de bord rafraîchi !")

    def on_stat_card_click(self, title, value):
        """Gère le clic sur une carte de statistique"""
        print(f"Carte '{title}' cliquée ! Valeur: {value}")

        # Afficher un dialog avec les détails
        message = f"Statistique: {title}\nValeur: {value}\n\nCette carte est maintenant interactive !"

        dialog = MDDialog(
            title=f"Détails - {title}",
            text=message,
            buttons=[
                MDFlatButton(
                    text="OK",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()

class ClientsScreen(MDScreen):
    """Écran de gestion des clients simplifié"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = "clients"
        self.build_screen()
    
    def build_screen(self):
        """Construit l'écran des clients"""
        layout = MDBoxLayout(orientation="vertical", spacing=dp(10), padding=dp(10))
        
        # Titre
        title = MDLabel(
            text="Gestion des Clients",
            theme_text_color="Primary",
            font_style="H5",
            size_hint_y=None,
            height=dp(60),
            halign="center"
        )
        layout.add_widget(title)
        
        # Bouton d'ajout
        add_btn = MDRaisedButton(
            text="Nouveau Client",
            size_hint=(None, None),
            size=(dp(150), dp(40)),
            pos_hint={"center_x": 0.5},
            on_release=self.add_client
        )
        layout.add_widget(add_btn)
        
        # Liste des clients
        scroll = MDScrollView()
        clients_list = MDList()
        
        try:
            clients = Client.get_all()
            for client in clients:
                item = ThreeLineListItem(
                    text=f"{client.prenom} {client.nom}",
                    secondary_text=client.entreprise or "Particulier",
                    tertiary_text=f"{client.email or ''} • {client.telephone or ''}",
                    on_release=lambda x, c=client: self.on_client_click(c)
                )
                clients_list.add_widget(item)
        except Exception as e:
            item = OneLineListItem(text=f"Erreur: {e}")
            clients_list.add_widget(item)
        
        scroll.add_widget(clients_list)
        layout.add_widget(scroll)

        self.add_widget(layout)

    def add_client(self, *args):
        """Ajoute un nouveau client"""
        print("Bouton 'Nouveau Client' cliqué !")
        # TODO: Implémenter le formulaire d'ajout de client
        self.show_message("Fonctionnalité 'Nouveau Client' en cours de développement")

    def on_client_click(self, client):
        """Gère le clic sur un client"""
        print(f"Client cliqué: {client.prenom} {client.nom}")
        self.show_message(f"Client sélectionné: {client.prenom} {client.nom}")

    def show_message(self, message):
        """Affiche un message simple"""
        print(f"Message: {message}")
        # Afficher un dialog
        dialog = MDDialog(
            title="Information",
            text=message,
            buttons=[
                MDFlatButton(
                    text="OK",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()

class ProduitsScreen(MDScreen):
    """Écran de gestion des produits simplifié"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = "produits"
        self.build_screen()
    
    def build_screen(self):
        """Construit l'écran des produits"""
        layout = MDBoxLayout(orientation="vertical", spacing=dp(10), padding=dp(10))
        
        # Titre
        title = MDLabel(
            text="Gestion des Produits",
            theme_text_color="Primary",
            font_style="H5",
            size_hint_y=None,
            height=dp(60),
            halign="center"
        )
        layout.add_widget(title)
        
        # Bouton d'ajout
        add_btn = MDRaisedButton(
            text="Nouveau Produit",
            size_hint=(None, None),
            size=(dp(150), dp(40)),
            pos_hint={"center_x": 0.5},
            on_release=self.add_produit
        )
        layout.add_widget(add_btn)
        
        # Liste des produits
        scroll = MDScrollView()
        produits_list = MDList()
        
        try:
            produits = Produit.get_all()
            for produit in produits:
                status = "RUPTURE" if produit.est_en_rupture() else "OK"
                item = ThreeLineListItem(
                    text=produit.nom,
                    secondary_text=f"{produit.prix_unitaire:.2f}€ • {produit.code_produit or 'Sans code'}",
                    tertiary_text=f"Stock: {produit.stock_actuel} • {status}",
                    on_release=lambda x, p=produit: self.on_produit_click(p)
                )
                produits_list.add_widget(item)
        except Exception as e:
            item = OneLineListItem(text=f"Erreur: {e}")
            produits_list.add_widget(item)
        
        scroll.add_widget(produits_list)
        layout.add_widget(scroll)

        self.add_widget(layout)

    def add_produit(self, *args):
        """Ajoute un nouveau produit"""
        print("Bouton 'Nouveau Produit' cliqué !")
        # TODO: Implémenter le formulaire d'ajout de produit
        self.show_message("Fonctionnalité 'Nouveau Produit' en cours de développement")

    def on_produit_click(self, produit):
        """Gère le clic sur un produit"""
        print(f"Produit cliqué: {produit.nom}")
        status = "EN RUPTURE" if produit.est_en_rupture() else "Stock OK"
        self.show_message(f"Produit: {produit.nom} - {status}")

    def show_message(self, message):
        """Affiche un message simple"""
        print(f"Message: {message}")
        # Afficher un dialog
        dialog = MDDialog(
            title="Information",
            text=message,
            buttons=[
                MDFlatButton(
                    text="OK",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()

class CommandesScreen(MDScreen):
    """Écran de gestion des commandes simplifié"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = "commandes"
        self.build_screen()
    
    def build_screen(self):
        """Construit l'écran des commandes"""
        layout = MDBoxLayout(orientation="vertical", spacing=dp(10), padding=dp(10))
        
        # Titre
        title = MDLabel(
            text="Gestion des Commandes",
            theme_text_color="Primary",
            font_style="H5",
            size_hint_y=None,
            height=dp(60),
            halign="center"
        )
        layout.add_widget(title)
        
        # Bouton d'ajout
        add_btn = MDRaisedButton(
            text="Nouvelle Commande",
            size_hint=(None, None),
            size=(dp(150), dp(40)),
            pos_hint={"center_x": 0.5},
            on_release=self.add_commande
        )
        layout.add_widget(add_btn)
        
        # Liste des commandes
        scroll = MDScrollView()
        commandes_list = MDList()
        
        try:
            commandes = Commande.get_all()
            for commande in commandes:
                client = Client.get_by_id(commande.client_id)
                client_nom = str(client) if client else "Client inconnu"

                item = ThreeLineListItem(
                    text=commande.numero_commande,
                    secondary_text=client_nom,
                    tertiary_text=f"{commande.statut} • {commande.total_ttc:.2f}€",
                    on_release=lambda x, c=commande: self.on_commande_click(c)
                )
                commandes_list.add_widget(item)
        except Exception as e:
            item = OneLineListItem(text=f"Erreur: {e}")
            commandes_list.add_widget(item)
        
        scroll.add_widget(commandes_list)
        layout.add_widget(scroll)

        self.add_widget(layout)

    def add_commande(self, *args):
        """Ajoute une nouvelle commande"""
        print("Bouton 'Nouvelle Commande' cliqué !")
        # TODO: Implémenter le formulaire d'ajout de commande
        self.show_message("Fonctionnalité 'Nouvelle Commande' en cours de développement")

    def on_commande_click(self, commande):
        """Gère le clic sur une commande"""
        print(f"Commande cliquée: {commande.numero_commande}")
        self.show_message(f"Commande: {commande.numero_commande} - {commande.statut}")

    def show_message(self, message):
        """Affiche un message simple"""
        print(f"Message: {message}")
        # Afficher un dialog
        dialog = MDDialog(
            title="Information",
            text=message,
            buttons=[
                MDFlatButton(
                    text="OK",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()

class GestionCommercialeSimpleApp(MDApp):
    """Application KivyMD simplifiée"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = "Gestion Commerciale - KivyMD"
        self.theme_cls.primary_palette = "Blue"
        self.theme_cls.theme_style = "Light"
    
    def build(self):
        """Construit l'interface principale"""
        # Initialiser la base de données
        try:
            self.db = DatabaseManager()
            print("✓ Base de données initialisée avec succès")
        except Exception as e:
            print(f"✗ Erreur lors de l'initialisation de la base de données: {e}")
            return MDLabel(text=f"Erreur: {e}")
        
        # Gestionnaire d'écrans
        self.screen_manager = MDScreenManager()
        
        # Ajouter les écrans
        self.screen_manager.add_widget(DashboardScreen())
        self.screen_manager.add_widget(ClientsScreen())
        self.screen_manager.add_widget(ProduitsScreen())
        self.screen_manager.add_widget(CommandesScreen())
        
        # Layout principal
        main_layout = MDBoxLayout(orientation="vertical")
        
        # Barre de navigation supérieure
        toolbar = MDTopAppBar(title="Gestion Commerciale")
        main_layout.add_widget(toolbar)
        
        # Contenu principal
        main_layout.add_widget(self.screen_manager)
        
        # Navigation inférieure
        bottom_nav = self.create_bottom_navigation()
        main_layout.add_widget(bottom_nav)
        
        return main_layout
    
    def create_bottom_navigation(self):
        """Crée la navigation inférieure"""
        bottom_nav = MDBottomNavigation()
        
        # Onglets de navigation
        tabs = [
            ("dashboard", "Accueil", "view-dashboard"),
            ("clients", "Clients", "account-group"),
            ("produits", "Produits", "package-variant"),
            ("commandes", "Commandes", "cart")
        ]
        
        for screen_name, title, icon in tabs:
            tab = MDBottomNavigationItem(
                name=screen_name,
                text=title,
                icon=icon
            )
            bottom_nav.add_widget(tab)
        
        # Lier la navigation
        bottom_nav.bind(on_tab_switch=self.on_tab_switch)
        
        return bottom_nav
    
    def on_tab_switch(self, instance_tabs, instance_tab, instance_tab_label, tab_text):
        """Gère le changement d'onglet"""
        self.screen_manager.current = instance_tab.name
        print(f"Navigation vers: {instance_tab.name}")

    def show_dialog(self, title, text):
        """Affiche un dialog d'information"""
        dialog = MDDialog(
            title=title,
            text=text,
            buttons=[
                MDFlatButton(
                    text="OK",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()

def main():
    """Point d'entrée principal"""
    try:
        app = GestionCommercialeSimpleApp()
        app.run()
    except Exception as e:
        print(f"Erreur lors du démarrage de l'application: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
