#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Application principale KivyMD avec navigation moderne
"""

import os
from kivymd.app import MDApp
from kivymd.uix.screenmanager import MDScreenManager
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.toolbar import MDTopAppBar
from kivymd.uix.bottomnavigation import MDBottomNavigation, MDBottomNavigationItem
from kivymd.uix.navigationdrawer import MD<PERSON>avi<PERSON>Drawer, MDNavigationDrawerMenu
from kivymd.uix.navigationdrawer import MDNavigationDrawerHeader, MDNavigationDrawerItem
from kivymd.uix.label import MDLabel
from kivymd.uix.snackbar import Snackbar

from kivy.metrics import dp
from kivy.core.window import Window

from .dashboard_screen import DashboardScreen
from .clients_screen import ClientsScreen
from .produits_screen import ProduitsScreen
from .commandes_screen import CommandesScreen
from ..database.database import DatabaseManager

class GestionCommercialeApp(MDApp):
    """Application principale avec interface KivyMD moderne"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = "Gestion Commerciale"
        
        # Configuration du thème
        self.theme_cls.primary_palette = "Blue"
        self.theme_cls.accent_palette = "Amber"
        self.theme_cls.theme_style = "Light"
        self.theme_cls.material_style = "M3"  # Material Design 3
        
        # Variables d'état
        self.current_screen = "dashboard"
        self.navigation_drawer = None
        
        # Configuration de la fenêtre
        Window.minimum_width = 360
        Window.minimum_height = 640
    
    def build(self):
        """Construit l'interface principale"""
        # Initialiser la base de données
        try:
            self.db = DatabaseManager()
            print("✓ Base de données initialisée avec succès")
        except Exception as e:
            print(f"✗ Erreur lors de l'initialisation de la base de données: {e}")
            return self.create_error_screen(str(e))
        
        # Créer le gestionnaire d'écrans
        self.screen_manager = MDScreenManager()
        
        # Ajouter les écrans
        self.add_screens()
        
        # Créer l'interface principale
        return self.create_main_interface()
    
    def add_screens(self):
        """Ajoute tous les écrans à l'application"""
        screens = [
            DashboardScreen(),
            ClientsScreen(),
            ProduitsScreen(), 
            CommandesScreen()
        ]
        
        for screen in screens:
            self.screen_manager.add_widget(screen)
    
    def create_main_interface(self):
        """Crée l'interface principale avec navigation"""
        # Layout principal
        main_layout = MDBoxLayout(orientation="vertical")
        
        # Barre d'outils supérieure
        self.toolbar = self.create_toolbar()
        main_layout.add_widget(self.toolbar)
        
        # Contenu principal avec navigation drawer
        content_layout = self.create_content_with_drawer()
        main_layout.add_widget(content_layout)
        
        # Navigation inférieure
        self.bottom_nav = self.create_bottom_navigation()
        main_layout.add_widget(self.bottom_nav)
        
        return main_layout
    
    def create_toolbar(self):
        """Crée la barre d'outils supérieure"""
        toolbar = MDTopAppBar(
            title="Gestion Commerciale",
            elevation=2,
            left_action_items=[["menu", lambda x: self.toggle_nav_drawer()]],
            right_action_items=[
                ["refresh", lambda x: self.refresh_current_screen()],
                ["dots-vertical", lambda x: self.show_menu()]
            ]
        )
        return toolbar
    
    def create_content_with_drawer(self):
        """Crée le contenu principal avec navigation drawer"""
        # Navigation drawer
        self.navigation_drawer = MDNavigationDrawer(
            radius=(0, 16, 16, 0),
            drawer_type="standard"
        )
        
        # En-tête du drawer
        header = MDNavigationDrawerHeader(
            title="Gestion Commerciale",
            title_color="white",
            text="Application de gestion",
            spacing=dp(4),
            padding=[dp(12), dp(56), dp(16), dp(12)],
            source="assets/header_bg.png"  # Image de fond optionnelle
        )
        
        # Menu du drawer
        drawer_menu = MDNavigationDrawerMenu()
        
        # Items du menu
        menu_items = [
            {
                "icon": "view-dashboard",
                "text": "Tableau de Bord",
                "screen": "dashboard"
            },
            {
                "icon": "account-group", 
                "text": "Clients",
                "screen": "clients"
            },
            {
                "icon": "package-variant",
                "text": "Produits", 
                "screen": "produits"
            },
            {
                "icon": "cart",
                "text": "Commandes",
                "screen": "commandes"
            },
            {"divider": True},
            {
                "icon": "receipt",
                "text": "Factures",
                "screen": "factures",
                "disabled": True
            },
            {
                "icon": "chart-line",
                "text": "Rapports",
                "screen": "rapports", 
                "disabled": True
            },
            {"divider": True},
            {
                "icon": "cog",
                "text": "Paramètres",
                "callback": self.show_settings
            },
            {
                "icon": "information",
                "text": "À propos",
                "callback": self.show_about
            }
        ]
        
        for item in menu_items:
            if item.get("divider"):
                # Ajouter un séparateur
                continue
            elif item.get("screen"):
                drawer_item = MDNavigationDrawerItem(
                    icon=item["icon"],
                    text=item["text"],
                    on_release=lambda x, screen=item["screen"]: self.navigate_to_screen(screen)
                )
                if item.get("disabled"):
                    drawer_item.disabled = True
                drawer_menu.add_widget(drawer_item)
            elif item.get("callback"):
                drawer_item = MDNavigationDrawerItem(
                    icon=item["icon"],
                    text=item["text"],
                    on_release=lambda x, cb=item["callback"]: cb()
                )
                drawer_menu.add_widget(drawer_item)
        
        # Assembler le drawer
        drawer_content = MDBoxLayout(orientation="vertical")
        drawer_content.add_widget(header)
        drawer_content.add_widget(drawer_menu)
        
        self.navigation_drawer.add_widget(drawer_content)
        
        # Layout avec drawer et contenu
        content_layout = MDBoxLayout()
        content_layout.add_widget(self.navigation_drawer)
        content_layout.add_widget(self.screen_manager)
        
        return content_layout
    
    def create_bottom_navigation(self):
        """Crée la navigation inférieure"""
        bottom_nav = MDBottomNavigation(
            panel_color=self.theme_cls.surface_color,
            selected_color_background=self.theme_cls.primary_color,
            text_color_active=self.theme_cls.primary_color,
            text_color_normal=self.theme_cls.disabled_hint_text_color
        )
        
        # Onglets principaux
        tabs = [
            {
                "name": "dashboard",
                "text": "Accueil", 
                "icon": "view-dashboard"
            },
            {
                "name": "clients",
                "text": "Clients",
                "icon": "account-group"
            },
            {
                "name": "produits", 
                "text": "Produits",
                "icon": "package-variant"
            },
            {
                "name": "commandes",
                "text": "Commandes", 
                "icon": "cart"
            }
        ]
        
        for tab in tabs:
            nav_item = MDBottomNavigationItem(
                name=tab["name"],
                text=tab["text"],
                icon=tab["icon"]
            )
            bottom_nav.add_widget(nav_item)
        
        # Lier les événements
        bottom_nav.bind(on_tab_switch=self.on_tab_switch)
        
        return bottom_nav
    
    def create_error_screen(self, error_message):
        """Crée un écran d'erreur"""
        layout = MDBoxLayout(
            orientation="vertical",
            spacing=dp(20),
            padding=dp(50)
        )
        
        error_label = MDLabel(
            text=f"Erreur d'initialisation:\n{error_message}",
            theme_text_color="Error",
            font_style="H6",
            halign="center",
            valign="center"
        )
        layout.add_widget(error_label)
        
        return layout
    
    def navigate_to_screen(self, screen_name):
        """Navigue vers un écran spécifique"""
        if screen_name in [screen.name for screen in self.screen_manager.screens]:
            self.screen_manager.current = screen_name
            self.current_screen = screen_name
            
            # Mettre à jour la navigation inférieure
            if hasattr(self, 'bottom_nav'):
                self.bottom_nav.switch_tab(screen_name)
            
            # Fermer le drawer
            if self.navigation_drawer:
                self.navigation_drawer.set_state("close")
            
            # Rafraîchir l'écran si nécessaire
            self.refresh_current_screen()
        else:
            snackbar = Snackbar()
            snackbar.text = f"Écran '{screen_name}' en cours de développement"
            snackbar.open()
    
    def on_tab_switch(self, instance_tabs, instance_tab, instance_tab_label, tab_text):
        """Gère le changement d'onglet dans la navigation inférieure"""
        screen_name = instance_tab.name
        
        if screen_name != self.current_screen:
            self.navigate_to_screen(screen_name)
    
    def toggle_nav_drawer(self):
        """Bascule l'état du navigation drawer"""
        if self.navigation_drawer:
            if self.navigation_drawer.state == "open":
                self.navigation_drawer.set_state("close")
            else:
                self.navigation_drawer.set_state("open")
    
    def refresh_current_screen(self):
        """Rafraîchit l'écran actuel"""
        try:
            current_screen = self.screen_manager.get_screen(self.current_screen)
            if hasattr(current_screen, 'refresh_data'):
                current_screen.refresh_data()
            else:
                snackbar = Snackbar()
                snackbar.text = "Données actualisées"
                snackbar.open()
        except Exception as e:
            snackbar = Snackbar()
            snackbar.text = f"Erreur lors de l'actualisation: {e}"
            snackbar.open()
    
    def show_menu(self):
        """Affiche le menu contextuel"""
        snackbar = Snackbar()
        snackbar.text = "Menu contextuel en cours de développement"
        snackbar.open()

    def show_settings(self):
        """Affiche les paramètres"""
        snackbar = Snackbar()
        snackbar.text = "Paramètres en cours de développement"
        snackbar.open()
        if self.navigation_drawer:
            self.navigation_drawer.set_state("close")
    
    def show_about(self):
        """Affiche les informations à propos"""
        from kivymd.uix.dialog import MDDialog
        from kivymd.uix.button import MDFlatButton
        
        dialog = MDDialog(
            title="À propos",
            text="Gestion Commerciale v2.0\n\n"
                 "Application moderne de gestion commerciale\n"
                 "développée avec Python et KivyMD\n\n"
                 "© 2024 - Interface Material Design",
            buttons=[
                MDFlatButton(
                    text="FERMER",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()
        
        if self.navigation_drawer:
            self.navigation_drawer.set_state("close")
    
    def on_start(self):
        """Appelé au démarrage de l'application"""
        # Définir l'écran initial
        self.navigate_to_screen("dashboard")
        
        # Message de bienvenue
        snackbar = Snackbar()
        snackbar.text = "✓ Application démarrée avec succès"
        snackbar.open()
    
    def on_pause(self):
        """Appelé quand l'application est mise en pause"""
        return True
    
    def on_resume(self):
        """Appelé quand l'application reprend"""
        # Rafraîchir les données
        self.refresh_current_screen()
