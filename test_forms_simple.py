#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test simple des formulaires fonctionnels
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent / "src"))

from kivymd.app import MDApp
from kivymd.uix.screenmanager import MDScreenManager
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.label import MDLabel

from kivy.metrics import dp

# Test direct des formulaires
print("🧪 Test des imports...")
try:
    from forms_kivymd_working import ClientFormScreen, ProduitFormScreen
    print("✅ Import réussi : forms_kivymd_working")
    FORMS_OK = True
except ImportError as e:
    print(f"❌ Erreur d'import : {e}")
    FORMS_OK = False

class TestScreen(MDScreen):
    """Écran de test simple"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = "test"
        self.build_screen()
    
    def build_screen(self):
        """Construit l'écran de test"""
        layout = MDBoxLayout(orientation="vertical", spacing=dp(20), padding=dp(20))
        
        title = MDLabel(
            text="Test Formulaires Fonctionnels",
            font_style="H4",
            theme_text_color="Primary",
            halign="center",
            size_hint_y=None,
            height=dp(60)
        )
        layout.add_widget(title)
        
        if FORMS_OK:
            status = MDLabel(
                text="✅ Formulaires disponibles",
                theme_text_color="Primary",
                halign="center",
                size_hint_y=None,
                height=dp(40)
            )
            layout.add_widget(status)
            
            # Test client
            client_btn = MDRaisedButton(
                text="TEST FORMULAIRE CLIENT",
                size_hint_y=None,
                height=dp(50),
                on_release=self.test_client
            )
            layout.add_widget(client_btn)
            
            # Test produit
            produit_btn = MDRaisedButton(
                text="TEST FORMULAIRE PRODUIT",
                size_hint_y=None,
                height=dp(50),
                on_release=self.test_produit
            )
            layout.add_widget(produit_btn)
            
        else:
            error = MDLabel(
                text="❌ Formulaires non disponibles",
                theme_text_color="Error",
                halign="center"
            )
            layout.add_widget(error)
        
        self.add_widget(layout)
    
    def test_client(self, *args):
        """Test du formulaire client"""
        print("🧪 TEST FORMULAIRE CLIENT...")
        try:
            app = MDApp.get_running_app()
            
            # Créer le formulaire
            print("🔧 Création du ClientFormScreen...")
            client_form = ClientFormScreen(callback=self.on_form_closed)
            print("✅ ClientFormScreen créé sans erreur")
            
            # Ajouter au screen manager
            if app.screen_manager.has_screen("client_form"):
                app.screen_manager.remove_widget(app.screen_manager.get_screen("client_form"))
            
            app.screen_manager.add_widget(client_form)
            app.screen_manager.current = "client_form"
            
            print("✅ FORMULAIRE CLIENT OUVERT AVEC SUCCÈS")
            
        except Exception as e:
            print(f"❌ ERREUR FORMULAIRE CLIENT: {e}")
            import traceback
            traceback.print_exc()
    
    def test_produit(self, *args):
        """Test du formulaire produit"""
        print("🧪 TEST FORMULAIRE PRODUIT...")
        try:
            app = MDApp.get_running_app()
            
            # Créer le formulaire
            print("🔧 Création du ProduitFormScreen...")
            produit_form = ProduitFormScreen(callback=self.on_form_closed)
            print("✅ ProduitFormScreen créé sans erreur")
            
            # Ajouter au screen manager
            if app.screen_manager.has_screen("produit_form"):
                app.screen_manager.remove_widget(app.screen_manager.get_screen("produit_form"))
            
            app.screen_manager.add_widget(produit_form)
            app.screen_manager.current = "produit_form"
            
            print("✅ FORMULAIRE PRODUIT OUVERT AVEC SUCCÈS")
            
        except Exception as e:
            print(f"❌ ERREUR FORMULAIRE PRODUIT: {e}")
            import traceback
            traceback.print_exc()
    
    def on_form_closed(self):
        """Callback quand un formulaire est fermé"""
        print("🔙 Retour au test")
        app = MDApp.get_running_app()
        app.screen_manager.current = "test"

class TestApp(MDApp):
    """Application de test"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = "Test Formulaires Fonctionnels"
        self.theme_cls.primary_palette = "Blue"
        self.theme_cls.theme_style = "Light"
    
    def build(self):
        """Construit l'application"""
        print("🚀 Démarrage du test...")
        
        # Gestionnaire d'écrans
        self.screen_manager = MDScreenManager()
        
        # Ajouter l'écran de test
        test_screen = TestScreen()
        self.screen_manager.add_widget(test_screen)
        
        return self.screen_manager

def main():
    """Point d'entrée"""
    try:
        print("=" * 60)
        print("  TEST FORMULAIRES FONCTIONNELS")
        print("=" * 60)
        print()
        
        app = TestApp()
        app.run()
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
