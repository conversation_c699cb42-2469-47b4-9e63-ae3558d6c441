#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Application KivyMD avec Dashboard et Formulaires Intégrés
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent / "src"))

from kivymd.app import MDApp
from kivymd.uix.screenmanager import MDScreenManager
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.button import MDRaisedButton, MDIconButton, MDFlatButton
from kivymd.uix.label import MDLabel
from kivymd.uix.card import MDCard
from kivymd.uix.dialog import MDDialog
from kivymd.uix.toolbar import MDTopAppBar

from kivy.metrics import dp

# Imports des formulaires
from formulaires_kivymd import ClientFormScreen, ProduitFormScreen
from formulaire_commande_kivymd import CommandeFormScreen

# Imports des modèles (optionnels)
try:
    from src.database.database import DatabaseManager
    from src.models.client import Client
    from src.models.produit import Produit
    from src.models.commande import Commande
    MODELS_AVAILABLE = True
except ImportError as e:
    print(f"⚠ Modèles non disponibles: {e}")
    MODELS_AVAILABLE = False

class DashboardScreen(MDScreen):
    """Dashboard avec boutons liés aux formulaires"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = "dashboard"
        self.build_screen()
    
    def build_screen(self):
        """Construit l'écran du dashboard"""
        # Layout principal
        main_layout = MDBoxLayout(orientation="vertical")
        
        # Barre de titre
        toolbar = MDTopAppBar(
            title="Gestion Commerciale - Dashboard",
            right_action_items=[["refresh", lambda x: self.refresh_dashboard()]]
        )
        main_layout.add_widget(toolbar)
        
        # Contenu scrollable
        scroll = MDScrollView()
        layout = MDBoxLayout(orientation="vertical", spacing=dp(10), padding=dp(10))
        
        # Titre
        title = MDLabel(
            text="Tableau de Bord",
            theme_text_color="Primary",
            font_style="H4",
            halign="center",
            size_hint_y=None,
            height=dp(60)
        )
        layout.add_widget(title)
        
        # Grille de cartes cliquables
        stats_grid = MDGridLayout(cols=2, spacing=dp(10), size_hint_y=None, height=dp(200))
        
        # Cartes avec statistiques et actions
        cards_data = [
            ("Clients", self.get_clients_count(), "account-group", self.open_client_form),
            ("Produits", self.get_produits_count(), "package-variant", self.open_produit_form),
            ("Commandes", self.get_commandes_count(), "cart", self.open_commande_form),
            ("CA Mois", self.get_ca_mois(), "currency-eur", self.show_ca_details)
        ]
        
        for title, value, icon, callback in cards_data:
            card = self.create_clickable_card(title, value, icon, callback)
            stats_grid.add_widget(card)
        
        layout.add_widget(stats_grid)
        
        # Section boutons directs
        buttons_section = MDBoxLayout(
            orientation="vertical",
            spacing=dp(10),
            size_hint_y=None,
            height=dp(200)
        )
        
        buttons_title = MDLabel(
            text="Actions Rapides",
            theme_text_color="Primary",
            font_style="H6",
            halign="center",
            size_hint_y=None,
            height=dp(40)
        )
        buttons_section.add_widget(buttons_title)
        
        # Boutons d'action rapide
        quick_buttons = [
            ("Nouveau Client", self.open_client_form),
            ("Nouveau Produit", self.open_produit_form),
            ("Nouvelle Commande", self.open_commande_form)
        ]
        
        for btn_text, callback in quick_buttons:
            btn = MDRaisedButton(
                text=btn_text,
                size_hint_y=None,
                height=dp(40),
                on_release=callback
            )
            buttons_section.add_widget(btn)
        
        layout.add_widget(buttons_section)
        
        # Statut
        self.status_label = MDLabel(
            text="Cliquez sur une carte ou un bouton pour commencer",
            theme_text_color="Secondary",
            halign="center",
            size_hint_y=None,
            height=dp(40)
        )
        layout.add_widget(self.status_label)
        
        scroll.add_widget(layout)
        main_layout.add_widget(scroll)
        self.add_widget(main_layout)
    
    def create_clickable_card(self, title, value, icon, callback):
        """Crée une carte cliquable"""
        card = MDCard(
            size_hint_y=None,
            height=dp(80),
            elevation=2,
            padding=dp(10),
            md_bg_color=(0.9, 0.9, 1, 1),
            on_release=callback
        )
        
        layout = MDBoxLayout(orientation="horizontal", spacing=dp(10))
        
        # Icône
        icon_btn = MDIconButton(
            icon=icon,
            size_hint=(None, None),
            size=(dp(40), dp(40)),
            disabled=True
        )
        layout.add_widget(icon_btn)
        
        # Texte
        text_layout = MDBoxLayout(orientation="vertical")
        
        title_label = MDLabel(
            text=title,
            theme_text_color="Primary",
            font_style="Subtitle1",
            bold=True
        )
        text_layout.add_widget(title_label)
        
        value_label = MDLabel(
            text=str(value),
            theme_text_color="Primary",
            font_style="H6"
        )
        text_layout.add_widget(value_label)
        
        layout.add_widget(text_layout)
        card.add_widget(layout)
        
        return card
    
    # Méthodes pour obtenir les statistiques
    def get_clients_count(self):
        """Obtient le nombre de clients"""
        if MODELS_AVAILABLE:
            try:
                clients = Client.get_all()
                return len([c for c in clients if getattr(c, 'actif', True)])
            except:
                return "N/A"
        return "25"  # Valeur simulée
    
    def get_produits_count(self):
        """Obtient le nombre de produits"""
        if MODELS_AVAILABLE:
            try:
                produits = Produit.get_all()
                return len([p for p in produits if getattr(p, 'actif', True)])
            except:
                return "N/A"
        return "150"  # Valeur simulée
    
    def get_commandes_count(self):
        """Obtient le nombre de commandes en attente"""
        if MODELS_AVAILABLE:
            try:
                commandes = Commande.get_all()
                return len([c for c in commandes if getattr(c, 'statut', '') == 'En attente'])
            except:
                return "N/A"
        return "8"  # Valeur simulée
    
    def get_ca_mois(self):
        """Obtient le CA du mois"""
        if MODELS_AVAILABLE:
            try:
                db = DatabaseManager()
                query = "SELECT COALESCE(SUM(total_ttc), 0) FROM commandes WHERE date_commande >= date('now', 'start of month')"
                result = db.execute_query(query)
                return f"{result[0][0]:.2f}€" if result else "0.00€"
            except:
                return "N/A"
        return "2,450€"  # Valeur simulée
    
    # Méthodes d'action
    def open_client_form(self, *args):
        """Ouvre le formulaire client"""
        print("🎯 Ouverture du formulaire client...")
        self.status_label.text = "Ouverture du formulaire client..."
        
        try:
            app = MDApp.get_running_app()
            
            # Créer le formulaire
            client_form = ClientFormScreen(callback=self.on_form_closed)
            
            # Ajouter au screen manager
            if app.screen_manager.has_screen("client_form"):
                app.screen_manager.remove_widget(app.screen_manager.get_screen("client_form"))
            
            app.screen_manager.add_widget(client_form)
            app.screen_manager.current = "client_form"
            
            print("✅ Formulaire client ouvert")
            
        except Exception as e:
            print(f"❌ Erreur: {e}")
            self.show_error(f"Erreur lors de l'ouverture du formulaire client: {e}")
    
    def open_produit_form(self, *args):
        """Ouvre le formulaire produit"""
        print("🎯 Ouverture du formulaire produit...")
        self.status_label.text = "Ouverture du formulaire produit..."
        
        try:
            app = MDApp.get_running_app()
            
            # Créer le formulaire
            produit_form = ProduitFormScreen(callback=self.on_form_closed)
            
            # Ajouter au screen manager
            if app.screen_manager.has_screen("produit_form"):
                app.screen_manager.remove_widget(app.screen_manager.get_screen("produit_form"))
            
            app.screen_manager.add_widget(produit_form)
            app.screen_manager.current = "produit_form"
            
            print("✅ Formulaire produit ouvert")
            
        except Exception as e:
            print(f"❌ Erreur: {e}")
            self.show_error(f"Erreur lors de l'ouverture du formulaire produit: {e}")
    
    def open_commande_form(self, *args):
        """Ouvre le formulaire commande"""
        print("🎯 Ouverture du formulaire commande...")
        self.status_label.text = "Ouverture du formulaire commande..."
        
        try:
            app = MDApp.get_running_app()
            
            # Créer le formulaire
            commande_form = CommandeFormScreen(callback=self.on_form_closed)
            
            # Ajouter au screen manager
            if app.screen_manager.has_screen("commande_form"):
                app.screen_manager.remove_widget(app.screen_manager.get_screen("commande_form"))
            
            app.screen_manager.add_widget(commande_form)
            app.screen_manager.current = "commande_form"
            
            print("✅ Formulaire commande ouvert")
            
        except Exception as e:
            print(f"❌ Erreur: {e}")
            self.show_error(f"Erreur lors de l'ouverture du formulaire commande: {e}")
    
    def show_ca_details(self, *args):
        """Affiche les détails du CA"""
        print("📊 Affichage des détails du CA...")
        self.status_label.text = "Affichage des détails du CA..."
        
        details = "Détails du Chiffre d'Affaires:\n\n"
        details += f"• CA Total: {self.get_ca_total()}\n"
        details += f"• CA Mois: {self.get_ca_mois()}\n"
        details += f"• CA Semaine: {self.get_ca_semaine()}\n"
        details += f"• Commandes Mois: {self.get_commandes_count()}"
        
        self.show_dialog("Chiffre d'Affaires", details)
    
    def get_ca_total(self):
        """Obtient le CA total"""
        return "15,750€"  # Valeur simulée
    
    def get_ca_semaine(self):
        """Obtient le CA de la semaine"""
        return "580€"  # Valeur simulée
    
    def refresh_dashboard(self, *args):
        """Rafraîchit le dashboard"""
        print("🔄 Rafraîchissement du dashboard...")
        self.status_label.text = "Dashboard rafraîchi !"
        
        # Reconstruire l'écran
        self.clear_widgets()
        self.build_screen()
        
        self.show_dialog("Information", "Dashboard actualisé avec succès !")
    
    def on_form_closed(self):
        """Appelé quand un formulaire est fermé"""
        print("🔙 Retour au dashboard depuis un formulaire")
        self.status_label.text = "Retour au dashboard"
        
        # Rafraîchir les données
        self.refresh_dashboard()
    
    def show_error(self, message):
        """Affiche un message d'erreur"""
        dialog = MDDialog(
            title="Erreur",
            text=message,
            buttons=[
                MDFlatButton(
                    text="OK",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()
    
    def show_dialog(self, title, text):
        """Affiche un dialog d'information"""
        dialog = MDDialog(
            title=title,
            text=text,
            buttons=[
                MDFlatButton(
                    text="OK",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()

class GestionCommercialeApp(MDApp):
    """Application principale avec dashboard et formulaires"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = "Gestion Commerciale - Dashboard & Formulaires"
        self.theme_cls.primary_palette = "Blue"
        self.theme_cls.theme_style = "Light"

    def build(self):
        """Construit l'application"""
        print("🚀 Démarrage de l'application...")

        # Initialiser la base de données si disponible
        if MODELS_AVAILABLE:
            try:
                self.db = DatabaseManager()
                print("✅ Base de données initialisée")
            except Exception as e:
                print(f"⚠ Erreur base de données: {e}")
        else:
            print("⚠ Mode simulation (sans base de données)")

        # Gestionnaire d'écrans
        self.screen_manager = MDScreenManager()

        # Ajouter l'écran dashboard
        dashboard = DashboardScreen()
        self.screen_manager.add_widget(dashboard)

        print("✅ Application prête")
        return self.screen_manager

def main():
    """Point d'entrée principal"""
    try:
        print("=" * 50)
        print("  GESTION COMMERCIALE - KIVYMD")
        print("  Dashboard avec Formulaires Intégrés")
        print("=" * 50)
        print()

        app = GestionCommercialeApp()
        app.run()

    except Exception as e:
        print(f"❌ Erreur lors du démarrage: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
