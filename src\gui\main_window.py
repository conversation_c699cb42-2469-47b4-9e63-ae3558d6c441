#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fenêtre principale de l'application
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from src.database.database import DatabaseManager
from src.gui.clients_window import ClientsWindow
from src.gui.produits_window import ProduitsWindow
from src.gui.commandes_window import CommandesWindow

class MainWindow:
    """Fenêtre principale de l'application"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Gestion Commerciale")
        self.root.geometry("1200x800")
        self.root.state('zoomed')  # Maximiser la fenêtre sur Windows
        
        # Initialiser la base de données
        try:
            self.db = DatabaseManager()
            print("Base de données initialisée avec succès")
        except Exception as e:
            messagebox.showerror("Erreur", f"Impossible d'initialiser la base de données: {e}")
            sys.exit(1)
        
        self.setup_ui()
        self.setup_menu()
        
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        # Frame principal
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configuration du redimensionnement
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # Titre
        title_label = ttk.Label(main_frame, text="Gestion Commerciale", 
                               font=("Arial", 24, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # Frame pour les boutons de navigation
        nav_frame = ttk.LabelFrame(main_frame, text="Navigation", padding="10")
        nav_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        
        # Boutons de navigation
        buttons_config = [
            ("👥 Gestion des Clients", self.open_clients, "Gérer les clients"),
            ("📦 Gestion des Produits", self.open_produits, "Gérer les produits et le stock"),
            ("🛒 Gestion des Commandes", self.open_commandes, "Créer et gérer les commandes"),
            ("🧾 Facturation", self.open_factures, "Générer et gérer les factures"),
            ("📊 Rapports", self.open_rapports, "Consulter les rapports et statistiques"),
        ]
        
        for i, (text, command, tooltip) in enumerate(buttons_config):
            btn = ttk.Button(nav_frame, text=text, command=command, width=30)
            btn.grid(row=i, column=0, pady=5, sticky=(tk.W, tk.E))
            # Ajouter un tooltip (simulation)
            self.create_tooltip(btn, tooltip)
        
        nav_frame.columnconfigure(0, weight=1)
        
        # Frame pour le contenu principal (dashboard)
        content_frame = ttk.LabelFrame(main_frame, text="Tableau de bord", padding="10")
        content_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        content_frame.columnconfigure(0, weight=1)
        content_frame.rowconfigure(0, weight=1)
        
        # Créer le tableau de bord
        self.create_dashboard(content_frame)
        
        # Barre de statut
        self.status_bar = ttk.Label(self.root, text="Prêt", relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.grid(row=1, column=0, sticky=(tk.W, tk.E))
    
    def create_dashboard(self, parent):
        """Crée le tableau de bord avec les statistiques"""
        # Notebook pour organiser les informations
        notebook = ttk.Notebook(parent)
        notebook.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Onglet Statistiques
        stats_frame = ttk.Frame(notebook)
        notebook.add(stats_frame, text="Statistiques")
        
        # Onglet Alertes
        alerts_frame = ttk.Frame(notebook)
        notebook.add(alerts_frame, text="Alertes")
        
        # Remplir l'onglet statistiques
        self.create_stats_tab(stats_frame)
        
        # Remplir l'onglet alertes
        self.create_alerts_tab(alerts_frame)
    
    def create_stats_tab(self, parent):
        """Crée l'onglet des statistiques"""
        # Frame pour les statistiques générales
        stats_general_frame = ttk.LabelFrame(parent, text="Statistiques générales", padding="10")
        stats_general_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        try:
            # Récupérer les statistiques
            stats = self.get_statistics()
            
            # Afficher les statistiques
            stats_labels = [
                ("Nombre de clients:", stats['nb_clients']),
                ("Nombre de produits:", stats['nb_produits']),
                ("Commandes en cours:", stats['commandes_en_cours']),
                ("Chiffre d'affaires du mois:", f"{stats['ca_mois']:.2f} €"),
            ]
            
            for i, (label, value) in enumerate(stats_labels):
                ttk.Label(stats_general_frame, text=label, font=("Arial", 10, "bold")).grid(
                    row=i//2, column=(i%2)*2, sticky=tk.W, padx=(0, 10), pady=2)
                ttk.Label(stats_general_frame, text=str(value)).grid(
                    row=i//2, column=(i%2)*2+1, sticky=tk.W, padx=(0, 20), pady=2)
                    
        except Exception as e:
            ttk.Label(stats_general_frame, text=f"Erreur lors du chargement des statistiques: {e}").grid(
                row=0, column=0, columnspan=4)
    
    def create_alerts_tab(self, parent):
        """Crée l'onglet des alertes"""
        alerts_frame = ttk.LabelFrame(parent, text="Alertes stock", padding="10")
        alerts_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # Treeview pour les produits en rupture
        columns = ("Produit", "Stock actuel", "Stock minimum")
        tree = ttk.Treeview(alerts_frame, columns=columns, show="headings", height=8)
        
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=150)
        
        tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(alerts_frame, orient=tk.VERTICAL, command=tree.yview)
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        tree.configure(yscrollcommand=scrollbar.set)
        
        alerts_frame.columnconfigure(0, weight=1)
        alerts_frame.rowconfigure(0, weight=1)
        
        # Charger les produits en rupture
        self.load_stock_alerts(tree)
    
    def get_statistics(self):
        """Récupère les statistiques pour le tableau de bord"""
        stats = {}
        
        # Nombre de clients actifs
        result = self.db.execute_query("SELECT COUNT(*) FROM clients WHERE actif=1")
        stats['nb_clients'] = result[0][0]
        
        # Nombre de produits actifs
        result = self.db.execute_query("SELECT COUNT(*) FROM produits WHERE actif=1")
        stats['nb_produits'] = result[0][0]
        
        # Commandes en cours
        result = self.db.execute_query("SELECT COUNT(*) FROM commandes WHERE statut='En attente'")
        stats['commandes_en_cours'] = result[0][0]
        
        # Chiffre d'affaires du mois
        result = self.db.execute_query("""
            SELECT COALESCE(SUM(total_ttc), 0) 
            FROM commandes 
            WHERE date_commande >= date('now', 'start of month')
        """)
        stats['ca_mois'] = result[0][0]
        
        return stats
    
    def load_stock_alerts(self, tree):
        """Charge les alertes de stock dans le treeview"""
        try:
            # Vider le treeview
            for item in tree.get_children():
                tree.delete(item)
            
            # Récupérer les produits en rupture
            query = """SELECT nom, stock_actuel, stock_minimum 
                      FROM produits 
                      WHERE stock_actuel <= stock_minimum AND actif=1 
                      ORDER BY nom"""
            results = self.db.execute_query(query)
            
            for row in results:
                tree.insert("", tk.END, values=(row[0], row[1], row[2]))
                
        except Exception as e:
            print(f"Erreur lors du chargement des alertes: {e}")
    
    def create_tooltip(self, widget, text):
        """Crée un tooltip pour un widget (simulation)"""
        def on_enter(event):
            self.status_bar.config(text=text)
        
        def on_leave(event):
            self.status_bar.config(text="Prêt")
        
        widget.bind("<Enter>", on_enter)
        widget.bind("<Leave>", on_leave)
    
    def setup_menu(self):
        """Configure le menu principal"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # Menu Fichier
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Fichier", menu=file_menu)
        file_menu.add_command(label="Actualiser", command=self.refresh_dashboard)
        file_menu.add_separator()
        file_menu.add_command(label="Quitter", command=self.quit_app)
        
        # Menu Gestion
        gestion_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Gestion", menu=gestion_menu)
        gestion_menu.add_command(label="Clients", command=self.open_clients)
        gestion_menu.add_command(label="Produits", command=self.open_produits)
        gestion_menu.add_command(label="Commandes", command=self.open_commandes)
        gestion_menu.add_command(label="Factures", command=self.open_factures)
        
        # Menu Aide
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Aide", menu=help_menu)
        help_menu.add_command(label="À propos", command=self.show_about)
    
    def refresh_dashboard(self):
        """Actualise le tableau de bord"""
        # Recharger les statistiques et alertes
        for widget in self.root.winfo_children():
            if isinstance(widget, ttk.Frame):
                self.setup_ui()
                break
    
    def open_clients(self):
        """Ouvre la fenêtre de gestion des clients"""
        try:
            ClientsWindow(self.root)
        except Exception as e:
            messagebox.showerror("Erreur", f"Impossible d'ouvrir la gestion des clients: {e}")
    
    def open_produits(self):
        """Ouvre la fenêtre de gestion des produits"""
        try:
            ProduitsWindow(self.root)
        except Exception as e:
            messagebox.showerror("Erreur", f"Impossible d'ouvrir la gestion des produits: {e}")
    
    def open_commandes(self):
        """Ouvre la fenêtre de gestion des commandes"""
        try:
            CommandesWindow(self.root)
        except Exception as e:
            messagebox.showerror("Erreur", f"Impossible d'ouvrir la gestion des commandes: {e}")
    
    def open_factures(self):
        """Ouvre la fenêtre de facturation"""
        messagebox.showinfo("Info", "Module de facturation en cours de développement")
    
    def open_rapports(self):
        """Ouvre la fenêtre des rapports"""
        messagebox.showinfo("Info", "Module de rapports en cours de développement")
    
    def show_about(self):
        """Affiche la boîte de dialogue À propos"""
        messagebox.showinfo("À propos", 
                           "Gestion Commerciale v1.0\n\n"
                           "Application de gestion commerciale\n"
                           "développée en Python avec Tkinter et SQLite\n\n"
                           "© 2024")
    
    def quit_app(self):
        """Quitte l'application"""
        if messagebox.askokcancel("Quitter", "Voulez-vous vraiment quitter l'application ?"):
            self.root.quit()
    
    def run(self):
        """Lance l'application"""
        self.root.mainloop()
