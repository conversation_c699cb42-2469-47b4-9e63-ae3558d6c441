#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Écran du tableau de bord KivyMD
"""

from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.label import MDLabel
from kivymd.uix.card import MDCard
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.list import MDList, OneLineListItem, TwoLineListItem
from kivymd.uix.button import MDIconButton
from kivymd.uix.scrollview import MDScrollView

from kivy.metrics import dp
from kivy.clock import Clock

from .base_screen import BaseScreen
from ..database.database import DatabaseManager
from ..models.client import Client
from ..models.produit import Produit
from ..models.commande import Commande

class DashboardScreen(BaseScreen):
    """Écran du tableau de bord avec statistiques et alertes"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = "dashboard"
        self.db = DatabaseManager()
        self.build_screen()
        
        # Programmer le rafraîchissement automatique
        Clock.schedule_interval(self.auto_refresh, 30)  # Toutes les 30 secondes
    
    def build_screen(self):
        """Construit l'écran du tableau de bord"""
        # Layout principal avec scroll
        scroll = MDScrollView()
        main_layout = MDBoxLayout(orientation="vertical")
        main_layout.spacing = dp(10)
        main_layout.padding = dp(10)
        
        # Titre avec bouton de rafraîchissement
        toolbar = self.create_toolbar(
            "Tableau de Bord",
            actions=[
                {
                    'type': 'icon',
                    'icon': 'refresh',
                    'callback': self.refresh_data
                }
            ]
        )
        main_layout.add_widget(toolbar)
        
        # Grille pour les cartes de statistiques
        self.stats_grid = MDGridLayout(
            cols=2, 
            spacing=dp(10), 
            size_hint_y=None,
            adaptive_height=True
        )
        main_layout.add_widget(self.stats_grid)
        
        # Section alertes
        self.alerts_section = self.create_alerts_section()
        main_layout.add_widget(self.alerts_section)
        
        # Section activité récente
        self.activity_section = self.create_activity_section()
        main_layout.add_widget(self.activity_section)
        
        scroll.add_widget(main_layout)
        self.add_widget(scroll)
        
        # Charger les données initiales
        self.load_dashboard_data()
    
    def load_dashboard_data(self):
        """Charge toutes les données du tableau de bord"""
        try:
            self.load_statistics()
            self.load_alerts()
            self.load_recent_activity()
        except Exception as e:
            self.handle_error(e, "Chargement du tableau de bord")
    
    def load_statistics(self):
        """Charge les statistiques dans les cartes"""
        self.stats_grid.clear_widgets()
        
        try:
            # Définir les statistiques à afficher
            stats_queries = [
                {
                    'title': 'Clients',
                    'query': 'SELECT COUNT(*) FROM clients WHERE actif=1',
                    'icon': 'account-group',
                    'color': 'blue'
                },
                {
                    'title': 'Produits',
                    'query': 'SELECT COUNT(*) FROM produits WHERE actif=1',
                    'icon': 'package-variant',
                    'color': 'green'
                },
                {
                    'title': 'Commandes',
                    'query': "SELECT COUNT(*) FROM commandes WHERE statut='En attente'",
                    'icon': 'cart',
                    'color': 'orange'
                },
                {
                    'title': 'CA Mois',
                    'query': "SELECT COALESCE(SUM(total_ttc), 0) FROM commandes WHERE date_commande >= date('now', 'start of month') AND statut NOT IN ('Annulée')",
                    'icon': 'currency-eur',
                    'color': 'purple',
                    'format': 'currency'
                }
            ]
            
            for stat in stats_queries:
                try:
                    result = self.db.execute_query(stat['query'])
                    value = result[0][0] if result else 0
                    
                    if stat.get('format') == 'currency':
                        value_text = f"{value:.2f}€"
                    else:
                        value_text = str(int(value))
                    
                    card = self.create_stat_card(
                        stat['title'], 
                        value_text, 
                        stat['icon'],
                        stat['color']
                    )
                    self.stats_grid.add_widget(card)
                    
                except Exception as e:
                    # Créer une carte d'erreur
                    card = self.create_stat_card(
                        stat['title'], 
                        "Erreur", 
                        "alert-circle",
                        "red"
                    )
                    self.stats_grid.add_widget(card)
                    
        except Exception as e:
            self.handle_error(e, "Chargement des statistiques")
    
    def create_stat_card(self, title, value, icon, color="blue"):
        """Crée une carte de statistique"""
        card = MDCard(
            size_hint_y=None,
            height=dp(120),
            elevation=3,
            padding=dp(15),
            md_bg_color="white",
            radius=[10]
        )
        
        layout = MDBoxLayout(orientation="vertical", spacing=dp(8))
        
        # En-tête avec icône et titre
        header = MDBoxLayout(
            orientation="horizontal", 
            size_hint_y=None, 
            height=dp(40),
            spacing=dp(10)
        )
        
        icon_btn = MDIconButton(
            icon=icon,
            theme_icon_color="Custom",
            text_color=color,
            size_hint=(None, None),
            size=(dp(40), dp(40)),
            disabled=True
        )
        header.add_widget(icon_btn)
        
        title_label = MDLabel(
            text=title,
            theme_text_color="Primary",
            font_style="Subtitle1",
            valign="center",
            bold=True
        )
        header.add_widget(title_label)
        
        layout.add_widget(header)
        
        # Valeur
        value_label = MDLabel(
            text=value,
            theme_text_color="Primary",
            font_style="H4",
            bold=True,
            halign="center",
            valign="center"
        )
        layout.add_widget(value_label)
        
        card.add_widget(layout)
        return card
    
    def create_alerts_section(self):
        """Crée la section des alertes de stock"""
        card = MDCard(
            size_hint_y=None,
            height=dp(250),
            elevation=3,
            padding=dp(15),
            md_bg_color="white",
            radius=[10]
        )
        
        layout = MDBoxLayout(orientation="vertical", spacing=dp(10))
        
        # En-tête
        header = MDBoxLayout(
            orientation="horizontal",
            size_hint_y=None,
            height=dp(40)
        )
        
        title = MDLabel(
            text="🚨 Alertes Stock",
            theme_text_color="Primary",
            font_style="H6",
            bold=True,
            valign="center"
        )
        header.add_widget(title)
        
        layout.add_widget(header)
        
        # Liste des alertes
        self.alerts_list = MDList()
        layout.add_widget(self.alerts_list)
        
        card.add_widget(layout)
        return card
    
    def load_alerts(self):
        """Charge les alertes de stock"""
        self.alerts_list.clear_widgets()
        
        try:
            query = """SELECT nom, stock_actuel, stock_minimum, prix_unitaire
                      FROM produits 
                      WHERE stock_actuel <= stock_minimum AND actif=1 
                      ORDER BY (stock_minimum - stock_actuel) DESC
                      LIMIT 5"""
            results = self.db.execute_query(query)
            
            if results:
                for row in results:
                    nom, stock_actuel, stock_minimum, prix = row
                    manque = stock_minimum - stock_actuel
                    
                    item = TwoLineListItem(
                        text=nom,
                        secondary_text=f"Stock: {stock_actuel} (Min: {stock_minimum}) • Manque: {manque}",
                        theme_text_color="Error"
                    )
                    self.alerts_list.add_widget(item)
            else:
                item = OneLineListItem(
                    text="✅ Aucune alerte de stock",
                    theme_text_color="Primary"
                )
                self.alerts_list.add_widget(item)
                
        except Exception as e:
            item = OneLineListItem(
                text=f"❌ Erreur: {e}",
                theme_text_color="Error"
            )
            self.alerts_list.add_widget(item)
    
    def create_activity_section(self):
        """Crée la section d'activité récente"""
        card = MDCard(
            size_hint_y=None,
            height=dp(250),
            elevation=3,
            padding=dp(15),
            md_bg_color="white",
            radius=[10]
        )
        
        layout = MDBoxLayout(orientation="vertical", spacing=dp(10))
        
        # En-tête
        title = MDLabel(
            text="📈 Activité Récente",
            theme_text_color="Primary",
            font_style="H6",
            bold=True,
            size_hint_y=None,
            height=dp(40)
        )
        layout.add_widget(title)
        
        # Liste d'activité
        self.activity_list = MDList()
        layout.add_widget(self.activity_list)
        
        card.add_widget(layout)
        return card
    
    def load_recent_activity(self):
        """Charge l'activité récente"""
        self.activity_list.clear_widgets()
        
        try:
            # Commandes récentes
            query = """SELECT c.numero_commande, c.date_commande, c.total_ttc, cl.nom, cl.prenom
                      FROM commandes c
                      JOIN clients cl ON c.client_id = cl.id
                      ORDER BY c.date_commande DESC
                      LIMIT 5"""
            results = self.db.execute_query(query)
            
            if results:
                for row in results:
                    numero, date, total, nom, prenom = row
                    
                    # Formater la date
                    try:
                        from datetime import datetime
                        if isinstance(date, str):
                            date_obj = datetime.fromisoformat(date.replace('Z', '+00:00'))
                            date_str = date_obj.strftime("%d/%m %H:%M")
                        else:
                            date_str = date.strftime("%d/%m %H:%M")
                    except:
                        date_str = str(date)[:10]
                    
                    item = TwoLineListItem(
                        text=f"Commande {numero}",
                        secondary_text=f"{prenom} {nom} • {total:.2f}€ • {date_str}",
                        theme_text_color="Primary"
                    )
                    self.activity_list.add_widget(item)
            else:
                item = OneLineListItem(
                    text="Aucune activité récente",
                    theme_text_color="Secondary"
                )
                self.activity_list.add_widget(item)
                
        except Exception as e:
            item = OneLineListItem(
                text=f"Erreur: {e}",
                theme_text_color="Error"
            )
            self.activity_list.add_widget(item)
    
    def refresh_data(self, *args):
        """Rafraîchit toutes les données du tableau de bord"""
        try:
            self.load_dashboard_data()
            self.show_success("Données actualisées")
        except Exception as e:
            self.handle_error(e, "Actualisation")
        finally:
            pass
    
    def auto_refresh(self, dt):
        """Rafraîchissement automatique silencieux"""
        try:
            self.load_dashboard_data()
        except Exception as e:
            print(f"Erreur lors du rafraîchissement automatique: {e}")
