@echo off
echo Installation des dépendances pour l'application de Gestion Commerciale...
echo.

REM Vérifier si Python est installé
python --version >nul 2>&1
if errorlevel 1 (
    echo ERREUR: Python n'est pas installé ou n'est pas dans le PATH
    echo Veuillez installer <PERSON> depuis https://python.org
    pause
    exit /b 1
)

REM Vérifier si pip est disponible
pip --version >nul 2>&1
if errorlevel 1 (
    echo ERREUR: pip n'est pas disponible
    echo Veuillez réinstaller Python avec pip
    pause
    exit /b 1
)

echo Installation des packages Python requis...
pip install pillow reportlab matplotlib

echo.
echo Installation terminée !
echo Vous pouvez maintenant lancer l'application avec start.bat
pause
