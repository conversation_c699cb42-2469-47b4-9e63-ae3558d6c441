@echo off
echo Installation de KivyMD pour l'application de Gestion Commerciale...
echo.

REM Vérifier si Python est installé
python --version >nul 2>&1
if errorlevel 1 (
    echo ERREUR: Python n'est pas installé ou n'est pas dans le PATH
    echo Veuillez installer <PERSON> depuis https://python.org
    pause
    exit /b 1
)

REM Vérifier si pip est disponible
pip --version >nul 2>&1
if errorlevel 1 (
    echo ERREUR: pip n'est pas disponible
    echo Veuillez réinstaller Python avec pip
    pause
    exit /b 1
)

echo Installation des dépendances KivyMD...
echo.

echo [1/4] Installation de Kivy...
pip install kivy[base]>=2.1.0

echo.
echo [2/4] Installation de KivyMD...
pip install kivymd>=1.1.1

echo.
echo [3/4] Installation des dépendances pour rapports...
pip install pillow reportlab matplotlib

echo.
echo [4/4] Installation des dépendances optionnelles...
pip install kivy[media]

echo.
echo ========================================
echo Installation terminée !
echo.
echo Vous pouvez maintenant lancer l'application avec :
echo   python main_kivymd.py
echo.
echo Ou utiliser start_kivymd.bat
echo ========================================
pause
