#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Écran de base pour KivyMD avec fonctionnalités communes
"""

from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.toolbar import MDTopAppBar
from kivymd.uix.button import MDRaisedButton, MDIconButton
from kivymd.uix.label import MDLabel
from kivymd.uix.snackbar import Snackbar
from kivymd.uix.dialog import MDDialog
from kivymd.uix.button import MDFlatButton

from kivy.metrics import dp

class BaseScreen(MDScreen):
    """Écran de base avec fonctionnalités communes"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.dialog = None
    
    def create_toolbar(self, title, actions=None):
        """Crée une barre d'outils standard"""
        toolbar_layout = MDBoxLayout(
            orientation="horizontal", 
            size_hint_y=None, 
            height=dp(60),
            spacing=dp(10),
            padding=[dp(10), dp(5)]
        )
        
        # Titre
        title_label = MDLabel(
            text=title,
            theme_text_color="Primary",
            font_style="H5",
            valign="center",
            size_hint_x=0.7
        )
        toolbar_layout.add_widget(title_label)
        
        # Actions (boutons)
        if actions:
            actions_layout = MDBoxLayout(
                orientation="horizontal",
                size_hint_x=0.3,
                spacing=dp(5)
            )
            
            for action in actions:
                if action['type'] == 'raised':
                    btn = MDRaisedButton(
                        text=action['text'],
                        size_hint=(None, None),
                        size=(dp(action.get('width', 120)), dp(40)),
                        on_release=action['callback']
                    )
                elif action['type'] == 'icon':
                    btn = MDIconButton(
                        icon=action['icon'],
                        size_hint=(None, None),
                        size=(dp(40), dp(40)),
                        on_release=action['callback']
                    )
                
                actions_layout.add_widget(btn)
            
            toolbar_layout.add_widget(actions_layout)
        
        return toolbar_layout
    
    def show_snackbar(self, message, duration=3):
        """Affiche un message snackbar"""
        from kivymd.uix.snackbar import Snackbar
        snackbar = Snackbar()
        snackbar.text = message
        snackbar.duration = duration
        snackbar.open()
    
    def show_error(self, message):
        """Affiche un message d'erreur"""
        self.show_snackbar(f"Erreur: {message}")
    
    def show_success(self, message):
        """Affiche un message de succès"""
        self.show_snackbar(f"✓ {message}")
    
    def show_confirmation_dialog(self, title, text, confirm_callback, cancel_callback=None):
        """Affiche une boîte de dialogue de confirmation"""
        if not self.dialog:
            self.dialog = MDDialog(
                title=title,
                text=text,
                buttons=[
                    MDFlatButton(
                        text="ANNULER",
                        theme_text_color="Custom",
                        text_color="grey",
                        on_release=lambda x: self.close_dialog(cancel_callback)
                    ),
                    MDFlatButton(
                        text="CONFIRMER",
                        theme_text_color="Custom", 
                        text_color="red",
                        on_release=lambda x: self.close_dialog(confirm_callback)
                    ),
                ],
            )
        else:
            self.dialog.title = title
            self.dialog.text = text
            self.dialog.buttons[0].on_release = lambda x: self.close_dialog(cancel_callback)
            self.dialog.buttons[1].on_release = lambda x: self.close_dialog(confirm_callback)
        
        self.dialog.open()
    
    def close_dialog(self, callback=None):
        """Ferme la boîte de dialogue"""
        if self.dialog:
            self.dialog.dismiss()
        if callback:
            callback()
    
    def create_empty_state(self, message, icon="information"):
        """Crée un état vide avec message"""
        layout = MDBoxLayout(
            orientation="vertical",
            spacing=dp(20),
            padding=dp(50)
        )
        
        # Icône
        icon_btn = MDIconButton(
            icon=icon,
            theme_icon_color="Custom",
            text_color="lightgrey",
            size_hint=(None, None),
            size=(dp(80), dp(80)),
            disabled=True
        )
        icon_btn.pos_hint = {"center_x": 0.5}
        layout.add_widget(icon_btn)
        
        # Message
        message_label = MDLabel(
            text=message,
            theme_text_color="Secondary",
            font_style="Body1",
            halign="center",
            valign="center"
        )
        layout.add_widget(message_label)
        
        return layout
    
    def handle_error(self, error, context=""):
        """Gère les erreurs de manière standardisée"""
        error_msg = f"{context}: {str(error)}" if context else str(error)
        print(f"Erreur - {error_msg}")
        self.show_error(error_msg)
    
    def refresh_data(self):
        """Méthode à surcharger pour rafraîchir les données"""
        pass
