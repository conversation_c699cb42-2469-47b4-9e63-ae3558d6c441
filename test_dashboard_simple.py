#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test simple du dashboard avec boutons fonctionnels
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent / "src"))

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.button import MDRaisedButton, MDIconButton, MDFlatButton
from kivymd.uix.label import MDLabel
from kivymd.uix.card import MDCard
from kivymd.uix.dialog import MDDialog
from kivymd.uix.scrollview import MDScrollView

from kivy.metrics import dp

class SimpleDashboardScreen(MDScreen):
    """Dashboard simple avec boutons fonctionnels"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = "dashboard"
        self.build_screen()
    
    def build_screen(self):
        """Construit l'écran du dashboard"""
        scroll = MDScrollView()
        layout = MDBoxLayout(orientation="vertical", spacing=dp(10), padding=dp(10))
        
        # Titre avec bouton de rafraîchissement
        header_layout = MDBoxLayout(orientation="horizontal", size_hint_y=None, height=dp(60))
        
        title = MDLabel(
            text="Dashboard - Test Boutons",
            theme_text_color="Primary",
            font_style="H4",
            halign="center"
        )
        header_layout.add_widget(title)
        
        refresh_btn = MDIconButton(
            icon="refresh",
            size_hint=(None, None),
            size=(dp(40), dp(40)),
            on_release=self.test_refresh_button,
            theme_icon_color="Primary"
        )
        header_layout.add_widget(refresh_btn)
        
        layout.add_widget(header_layout)
        
        # Grille de cartes cliquables
        stats_grid = MDGridLayout(cols=2, spacing=dp(10), size_hint_y=None, height=dp(200))
        
        # Cartes de test avec callbacks
        cards_data = [
            ("Clients", "25", self.test_clients_button),
            ("Produits", "150", self.test_produits_button),
            ("Commandes", "8", self.test_commandes_button),
            ("CA Mois", "2,450€", self.test_ca_button)
        ]
        
        for title, value, callback in cards_data:
            card = self.create_clickable_card(title, value, callback)
            stats_grid.add_widget(card)
        
        layout.add_widget(stats_grid)
        
        # Section de test avec boutons directs
        test_section = MDBoxLayout(
            orientation="vertical",
            spacing=dp(10),
            size_hint_y=None,
            height=dp(300)
        )
        
        test_title = MDLabel(
            text="Tests Directs des Boutons",
            theme_text_color="Primary",
            font_style="H6",
            halign="center",
            size_hint_y=None,
            height=dp(40)
        )
        test_section.add_widget(test_title)
        
        # Boutons de test directs
        test_buttons = [
            ("Test Bouton 1", self.test_button_1),
            ("Test Bouton 2", self.test_button_2),
            ("Test Dialog", self.test_dialog),
            ("Test Navigation", self.test_navigation)
        ]
        
        for btn_text, callback in test_buttons:
            btn = MDRaisedButton(
                text=btn_text,
                size_hint_y=None,
                height=dp(40),
                on_release=callback
            )
            test_section.add_widget(btn)
        
        layout.add_widget(test_section)
        
        # Label de statut
        self.status_label = MDLabel(
            text="Cliquez sur un bouton pour tester",
            theme_text_color="Secondary",
            halign="center",
            size_hint_y=None,
            height=dp(40)
        )
        layout.add_widget(self.status_label)
        
        scroll.add_widget(layout)
        self.add_widget(scroll)
    
    def create_clickable_card(self, title, value, callback):
        """Crée une carte cliquable avec callback"""
        card = MDCard(
            size_hint_y=None,
            height=dp(80),
            elevation=2,
            padding=dp(10),
            md_bg_color=(0.9, 0.9, 1, 1),
            on_release=callback
        )
        
        layout = MDBoxLayout(orientation="vertical")
        
        title_label = MDLabel(
            text=title,
            theme_text_color="Primary",
            font_style="Subtitle1",
            size_hint_y=None,
            height=dp(30),
            halign="center",
            bold=True
        )
        layout.add_widget(title_label)
        
        value_label = MDLabel(
            text=value,
            theme_text_color="Primary",
            font_style="H6",
            halign="center"
        )
        layout.add_widget(value_label)
        
        card.add_widget(layout)
        return card
    
    # Callbacks pour les cartes
    def test_clients_button(self, *args):
        """Test du bouton Clients"""
        print("Bouton Clients cliqué !")
        self.status_label.text = "✓ Bouton Clients fonctionne !"
        self.show_dialog("Test Clients", "Le bouton Clients fonctionne parfaitement !\n\nDans l'app complète, ceci ouvrirait le formulaire client.")
    
    def test_produits_button(self, *args):
        """Test du bouton Produits"""
        print("Bouton Produits cliqué !")
        self.status_label.text = "✓ Bouton Produits fonctionne !"
        self.show_dialog("Test Produits", "Le bouton Produits fonctionne parfaitement !\n\nDans l'app complète, ceci ouvrirait le formulaire produit.")
    
    def test_commandes_button(self, *args):
        """Test du bouton Commandes"""
        print("Bouton Commandes cliqué !")
        self.status_label.text = "✓ Bouton Commandes fonctionne !"
        self.show_dialog("Test Commandes", "Le bouton Commandes fonctionne parfaitement !\n\nDans l'app complète, ceci ouvrirait le formulaire commande.")
    
    def test_ca_button(self, *args):
        """Test du bouton CA"""
        print("Bouton CA cliqué !")
        self.status_label.text = "✓ Bouton CA fonctionne !"
        self.show_dialog("Test CA", "Le bouton CA fonctionne parfaitement !\n\nDans l'app complète, ceci afficherait les détails du chiffre d'affaires.")
    
    def test_refresh_button(self, *args):
        """Test du bouton rafraîchir"""
        print("Bouton Rafraîchir cliqué !")
        self.status_label.text = "✓ Bouton Rafraîchir fonctionne !"
        self.show_dialog("Test Rafraîchir", "Le bouton Rafraîchir fonctionne parfaitement !\n\nLe dashboard a été rafraîchi.")
    
    # Callbacks pour les boutons de test directs
    def test_button_1(self, *args):
        """Test bouton 1"""
        print("Test Bouton 1 cliqué !")
        self.status_label.text = "✓ Test Bouton 1 fonctionne !"
        self.show_dialog("Test Bouton 1", "Le Test Bouton 1 fonctionne parfaitement !")
    
    def test_button_2(self, *args):
        """Test bouton 2"""
        print("Test Bouton 2 cliqué !")
        self.status_label.text = "✓ Test Bouton 2 fonctionne !"
        self.show_dialog("Test Bouton 2", "Le Test Bouton 2 fonctionne parfaitement !")
    
    def test_dialog(self, *args):
        """Test dialog"""
        print("Test Dialog cliqué !")
        self.status_label.text = "✓ Test Dialog fonctionne !"
        self.show_dialog("Test Dialog", "Ce dialog de test fonctionne parfaitement !\n\nTous les dialogs sont opérationnels.")
    
    def test_navigation(self, *args):
        """Test navigation"""
        print("Test Navigation cliqué !")
        self.status_label.text = "✓ Test Navigation fonctionne !"
        self.show_dialog("Test Navigation", "La navigation fonctionne parfaitement !\n\nDans l'app complète, ceci naviguerait vers d'autres écrans.")
    
    def show_dialog(self, title, text):
        """Affiche un dialog"""
        dialog = MDDialog(
            title=title,
            text=text,
            buttons=[
                MDFlatButton(
                    text="OK",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()

class TestDashboardApp(MDApp):
    """Application de test du dashboard"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = "Test Dashboard - Boutons Fonctionnels"
        self.theme_cls.primary_palette = "Blue"
        self.theme_cls.theme_style = "Light"
    
    def build(self):
        """Construit l'application de test"""
        return SimpleDashboardScreen()

def main():
    """Point d'entrée"""
    try:
        print("=== Test Dashboard - Boutons Fonctionnels ===")
        print("Démarrage du test...")
        app = TestDashboardApp()
        app.run()
    except Exception as e:
        print(f"Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
