#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Modèle Commande
"""

from datetime import datetime
from src.database.database import DatabaseManager

class LigneCommande:
    """Modèle pour une ligne de commande"""
    
    def __init__(self, id=None, commande_id=None, produit_id=None, 
                 quantite=0, prix_unitaire=0.0, total_ligne=0.0):
        self.id = id
        self.commande_id = commande_id
        self.produit_id = produit_id
        self.quantite = quantite
        self.prix_unitaire = prix_unitaire
        self.total_ligne = total_ligne or (quantite * prix_unitaire)

class Commande:
    """Modèle pour gérer les commandes"""
    
    def __init__(self, id=None, numero_commande="", client_id=None,
                 date_commande=None, statut="En attente", total_ht=0.0,
                 tva=20.0, total_ttc=0.0, notes=""):
        self.id = id
        self.numero_commande = numero_commande
        self.client_id = client_id
        self.date_commande = date_commande or datetime.now()
        self.statut = statut
        self.total_ht = total_ht
        self.tva = tva
        self.total_ttc = total_ttc
        self.notes = notes
        self.lignes = []
        self.db = DatabaseManager()
    
    def generer_numero_commande(self):
        """Génère un numéro de commande unique"""
        if not self.numero_commande:
            date_str = datetime.now().strftime("%Y%m%d")
            # Compter les commandes du jour
            query = "SELECT COUNT(*) FROM commandes WHERE date_commande >= date('now')"
            result = self.db.execute_query(query)
            count = result[0][0] + 1
            self.numero_commande = f"CMD-{date_str}-{count:04d}"
    
    def ajouter_ligne(self, produit_id, quantite, prix_unitaire):
        """Ajoute une ligne à la commande"""
        total_ligne = quantite * prix_unitaire
        ligne = LigneCommande(
            commande_id=self.id,
            produit_id=produit_id,
            quantite=quantite,
            prix_unitaire=prix_unitaire,
            total_ligne=total_ligne
        )
        self.lignes.append(ligne)
        self.calculer_totaux()
    
    def supprimer_ligne(self, index):
        """Supprime une ligne de la commande"""
        if 0 <= index < len(self.lignes):
            del self.lignes[index]
            self.calculer_totaux()
    
    def calculer_totaux(self):
        """Calcule les totaux de la commande"""
        self.total_ht = sum(ligne.total_ligne for ligne in self.lignes)
        self.total_ttc = self.total_ht * (1 + self.tva / 100)
    
    def save(self):
        """Sauvegarde la commande en base de données"""
        if not self.numero_commande:
            self.generer_numero_commande()
        
        self.calculer_totaux()
        
        if self.id is None:
            # Nouvelle commande
            query = """INSERT INTO commandes (numero_commande, client_id, date_commande,
                      statut, total_ht, tva, total_ttc, notes) 
                      VALUES (?, ?, ?, ?, ?, ?, ?, ?)"""
            params = (self.numero_commande, self.client_id, self.date_commande,
                     self.statut, self.total_ht, self.tva, self.total_ttc, self.notes)
            self.id = self.db.execute_query(query, params)
        else:
            # Mise à jour commande existante
            query = """UPDATE commandes SET numero_commande=?, client_id=?, date_commande=?,
                      statut=?, total_ht=?, tva=?, total_ttc=?, notes=? 
                      WHERE id=?"""
            params = (self.numero_commande, self.client_id, self.date_commande,
                     self.statut, self.total_ht, self.tva, self.total_ttc, self.notes, self.id)
            self.db.execute_query(query, params)
        
        # Sauvegarder les lignes
        self.sauvegarder_lignes()
        return self.id
    
    def sauvegarder_lignes(self):
        """Sauvegarde les lignes de commande"""
        if self.id:
            # Supprimer les anciennes lignes
            query = "DELETE FROM lignes_commande WHERE commande_id=?"
            self.db.execute_query(query, (self.id,))
            
            # Insérer les nouvelles lignes
            if self.lignes:
                query = """INSERT INTO lignes_commande (commande_id, produit_id, quantite, 
                          prix_unitaire, total_ligne) VALUES (?, ?, ?, ?, ?)"""
                params_list = []
                for ligne in self.lignes:
                    params_list.append((self.id, ligne.produit_id, ligne.quantite,
                                      ligne.prix_unitaire, ligne.total_ligne))
                self.db.execute_many(query, params_list)
    
    def charger_lignes(self):
        """Charge les lignes de commande depuis la base"""
        if self.id:
            query = """SELECT lc.*, p.nom as produit_nom 
                      FROM lignes_commande lc 
                      JOIN produits p ON lc.produit_id = p.id 
                      WHERE lc.commande_id=?"""
            results = self.db.execute_query(query, (self.id,))
            
            self.lignes = []
            for row in results:
                ligne = LigneCommande(
                    id=row['id'],
                    commande_id=row['commande_id'],
                    produit_id=row['produit_id'],
                    quantite=row['quantite'],
                    prix_unitaire=row['prix_unitaire'],
                    total_ligne=row['total_ligne']
                )
                self.lignes.append(ligne)
    
    @classmethod
    def get_by_id(cls, commande_id):
        """Récupère une commande par son ID"""
        db = DatabaseManager()
        query = "SELECT * FROM commandes WHERE id=?"
        result = db.execute_query(query, (commande_id,))
        if result:
            row = result[0]
            commande = cls(
                id=row['id'], numero_commande=row['numero_commande'],
                client_id=row['client_id'], date_commande=row['date_commande'],
                statut=row['statut'], total_ht=row['total_ht'],
                tva=row['tva'], total_ttc=row['total_ttc'],
                notes=row['notes']
            )
            commande.charger_lignes()
            return commande
        return None
    
    @classmethod
    def get_all(cls):
        """Récupère toutes les commandes"""
        db = DatabaseManager()
        query = """SELECT c.*, cl.nom as client_nom, cl.prenom as client_prenom 
                  FROM commandes c 
                  JOIN clients cl ON c.client_id = cl.id 
                  ORDER BY c.date_commande DESC"""
        results = db.execute_query(query)
        
        commandes = []
        for row in results:
            commande = cls(
                id=row['id'], numero_commande=row['numero_commande'],
                client_id=row['client_id'], date_commande=row['date_commande'],
                statut=row['statut'], total_ht=row['total_ht'],
                tva=row['tva'], total_ttc=row['total_ttc'],
                notes=row['notes']
            )
            commandes.append(commande)
        return commandes
    
    @classmethod
    def get_by_client(cls, client_id):
        """Récupère les commandes d'un client"""
        db = DatabaseManager()
        query = "SELECT * FROM commandes WHERE client_id=? ORDER BY date_commande DESC"
        results = db.execute_query(query, (client_id,))
        
        commandes = []
        for row in results:
            commande = cls(
                id=row['id'], numero_commande=row['numero_commande'],
                client_id=row['client_id'], date_commande=row['date_commande'],
                statut=row['statut'], total_ht=row['total_ht'],
                tva=row['tva'], total_ttc=row['total_ttc'],
                notes=row['notes']
            )
            commandes.append(commande)
        return commandes
    
    def __str__(self):
        """Représentation textuelle de la commande"""
        return f"{self.numero_commande} - {self.total_ttc:.2f}€ ({self.statut})"
