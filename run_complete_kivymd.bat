@echo off
echo ========================================
echo   Application KivyMD Complete
echo   Gestion Commerciale avec Formulaires
echo ========================================
echo.

cd /d "c:\Users\<USER>\Desktop\gui"

echo Verification des dependances...
python -c "import kivymd; print('✓ KivyMD disponible')" 2>nul
if errorlevel 1 (
    echo ✗ KivyMD non trouve. Installation...
    pip install kivymd
)

echo.
echo Lancement de l'application complete...
python app_complete_kivymd.py

echo.
echo Application fermee.
pause
