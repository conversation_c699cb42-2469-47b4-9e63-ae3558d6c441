# Application de Gestion Commerciale

Une application desktop complète de gestion commerciale développée en Python avec Tkinter et SQLite. Interface moderne et intuitive pour gérer clients, produits, commandes et suivre les ventes.

![Python](https://img.shields.io/badge/Python-3.7+-blue.svg)
![Tkinter](https://img.shields.io/badge/GUI-Tkinter-green.svg)
![SQLite](https://img.shields.io/badge/Database-SQLite-orange.svg)
![Windows](https://img.shields.io/badge/Platform-Windows-lightgrey.svg)

## ✨ Fonctionnalités

### 📊 Tableau de Bord
- Vue d'ensemble des statistiques commerciales
- Alertes de stock en temps réel
- Indicateurs de performance (CA, commandes, clients)

### 👥 Gestion des Clients
- Ajout, modification, suppression de clients
- Recherche avancée
- Historique des commandes par client
- Gestion des informations de contact et facturation

### 📦 Gestion des Produits
- Catalogue de produits avec codes et descriptions
- Gestion des stocks avec alertes de rupture
- Ajustement de stock en temps réel
- Catégorisation des produits
- Suivi des prix et marges

### 🛒 Gestion des Commandes
- Création de commandes multi-produits
- Suivi du statut (En attente → Confirmée → Expédiée → Livrée)
- Calcul automatique des totaux HT/TTC
- Gestion de la TVA personnalisable
- Interface intuitive avec codes couleur

### 🧾 Facturation (En développement)
- Génération de factures depuis les commandes
- Suivi des paiements
- Gestion des échéances

### 📈 Rapports et Statistiques (En développement)
- Rapports de ventes par période
- Top des produits vendus
- Analyse des meilleurs clients
- Export CSV des données

## 🚀 Installation Rapide

### Méthode 1 : Installation automatique (Recommandée)
1. **Téléchargez** le projet
2. **Double-cliquez** sur `install_requirements.bat` pour installer les dépendances
3. **Double-cliquez** sur `start.bat` pour lancer l'application

### Méthode 2 : Installation manuelle
```bash
# Cloner ou télécharger le projet
cd gui

# Installer les dépendances
pip install pillow reportlab matplotlib

# Lancer l'application
python main.py
```

### Méthode 3 : Test de l'installation
```bash
# Vérifier que tout fonctionne
python test_app.py
```

## 📁 Structure du Projet

```
gui/
├── 📄 main.py                    # Point d'entrée principal
├── 🚀 start.bat                  # Script de démarrage Windows
├── ⚙️ install_requirements.bat   # Installation automatique
├── 🧪 test_app.py               # Tests de l'application
├── 📋 requirements.txt          # Dépendances Python
├── 📖 GUIDE_UTILISATION.md      # Guide détaillé
├── 📊 data/                     # Base de données SQLite
│   └── gestion_commerciale.db
├── 💾 exports/                  # Rapports exportés
├── 🔧 src/                      # Code source
│   ├── 🗄️ database/            # Gestion base de données
│   │   └── database.py
│   ├── 📋 models/               # Modèles de données
│   │   ├── client.py
│   │   ├── produit.py
│   │   ├── commande.py
│   │   └── facture.py
│   ├── 🖥️ gui/                 # Interface graphique
│   │   ├── main_window.py
│   │   ├── clients_window.py
│   │   ├── produits_window.py
│   │   └── commandes_window.py
│   └── 🛠️ utils/               # Utilitaires
│       └── rapports.py
```

## 🎯 Utilisation

### Démarrage Rapide
1. **Lancez** l'application avec `start.bat` ou `python main.py`
2. **Explorez** le tableau de bord pour voir les statistiques
3. **Ajoutez** vos premiers clients dans "Gestion des Clients"
4. **Créez** votre catalogue dans "Gestion des Produits"
5. **Commencez** à saisir des commandes

### Workflow Typique
```
1. Clients 👥 → Ajouter/Modifier les informations clients
2. Produits 📦 → Gérer le catalogue et les stocks
3. Commandes 🛒 → Créer et suivre les commandes
4. Tableau de bord 📊 → Analyser les performances
```

## 🔧 Configuration Requise

- **OS** : Windows 10 ou supérieur
- **Python** : 3.7 ou supérieur
- **RAM** : 512 MB minimum
- **Espace disque** : 100 MB
- **Résolution** : 1024x768 minimum (1920x1080 recommandée)

## 📚 Documentation

- **[Guide d'Utilisation Complet](GUIDE_UTILISATION.md)** - Instructions détaillées
- **Code documenté** - Commentaires en français dans tout le code
- **Tests inclus** - Script de test pour vérifier l'installation

## 🎨 Captures d'Écran

L'application propose une interface moderne avec :
- **Tableau de bord** avec statistiques en temps réel
- **Gestion clients** avec recherche et filtres
- **Catalogue produits** avec alertes de stock
- **Commandes** avec codes couleur par statut
- **Navigation intuitive** entre les modules

## 🔒 Sécurité et Sauvegarde

- **Base de données locale** : Vos données restent sur votre machine
- **Sauvegarde simple** : Copiez le dossier `data/`
- **Pas de connexion internet requise** : Fonctionne hors ligne

## 🚨 Résolution de Problèmes

### L'application ne démarre pas
```bash
# Vérifier Python
python --version

# Réinstaller les dépendances
pip install --upgrade pillow reportlab matplotlib

# Tester l'installation
python test_app.py
```

### Erreurs courantes
- **"Module not found"** → Exécutez `install_requirements.bat`
- **"Database error"** → Supprimez le dossier `data/` et relancez
- **Interface qui freeze** → Fermez et relancez l'application

## 🤝 Contribution

Ce projet est conçu pour être facilement extensible :
- **Modèles** : Ajoutez de nouvelles entités dans `src/models/`
- **Interface** : Créez de nouvelles fenêtres dans `src/gui/`
- **Rapports** : Étendez `src/utils/rapports.py`

## 📄 Licence

Projet libre d'utilisation pour usage personnel et commercial.

## 📞 Support

- **Documentation** : Consultez `GUIDE_UTILISATION.md`
- **Tests** : Utilisez `python test_app.py` pour diagnostiquer
- **Logs** : Vérifiez la console pour les messages d'erreur

---

**Version** : 1.0
**Développé avec** : Python 3, Tkinter, SQLite
**Compatible** : Windows 10+
