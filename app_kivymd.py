#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Application de Gestion Commerciale - Version KivyMD
Point d'entrée principal pour l'interface moderne
"""

import sys
import os
from pathlib import Path

# Ajouter le répertoire src au path
sys.path.append(str(Path(__file__).parent / "src"))

# Configuration Kivy
os.environ['KIVY_WINDOW_ICON'] = 'assets/icon.png'

# Import de l'application principale
from src.kivymd_gui.main_app import GestionCommercialeApp


def main():
    """Point d'entrée principal"""
    try:
        app = GestionCommercialeApp()
        app.run()
    except Exception as e:
        print(f"Erreur lors du démarrage de l'application: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
