#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Utilitaires pour générer des rapports
"""

from datetime import datetime, timedelta
from src.database.database import DatabaseManager

class RapportGenerator:
    """Générateur de rapports"""
    
    def __init__(self):
        self.db = DatabaseManager()
    
    def rapport_ventes_periode(self, date_debut, date_fin):
        """Génère un rapport des ventes pour une période donnée"""
        query = """
            SELECT 
                DATE(c.date_commande) as date,
                COUNT(c.id) as nb_commandes,
                SUM(c.total_ht) as total_ht,
                SUM(c.total_ttc) as total_ttc
            FROM commandes c
            WHERE c.date_commande BETWEEN ? AND ?
            AND c.statut NOT IN ('Annulée')
            GROUP BY DATE(c.date_commande)
            ORDER BY date
        """
        
        results = self.db.execute_query(query, (date_debut, date_fin))
        
        rapport = {
            'periode': f"{date_debut} - {date_fin}",
            'details': [],
            'totaux': {
                'nb_commandes': 0,
                'total_ht': 0.0,
                'total_ttc': 0.0
            }
        }
        
        for row in results:
            detail = {
                'date': row[0],
                'nb_commandes': row[1],
                'total_ht': row[2] or 0.0,
                'total_ttc': row[3] or 0.0
            }
            rapport['details'].append(detail)
            
            # Ajouter aux totaux
            rapport['totaux']['nb_commandes'] += detail['nb_commandes']
            rapport['totaux']['total_ht'] += detail['total_ht']
            rapport['totaux']['total_ttc'] += detail['total_ttc']
        
        return rapport
    
    def rapport_produits_vendus(self, date_debut, date_fin):
        """Génère un rapport des produits les plus vendus"""
        query = """
            SELECT 
                p.nom,
                p.code_produit,
                SUM(lc.quantite) as quantite_vendue,
                SUM(lc.total_ligne) as chiffre_affaires
            FROM lignes_commande lc
            JOIN produits p ON lc.produit_id = p.id
            JOIN commandes c ON lc.commande_id = c.id
            WHERE c.date_commande BETWEEN ? AND ?
            AND c.statut NOT IN ('Annulée')
            GROUP BY p.id, p.nom, p.code_produit
            ORDER BY quantite_vendue DESC
            LIMIT 20
        """
        
        results = self.db.execute_query(query, (date_debut, date_fin))
        
        rapport = {
            'periode': f"{date_debut} - {date_fin}",
            'produits': []
        }
        
        for row in results:
            produit = {
                'nom': row[0],
                'code': row[1] or '',
                'quantite_vendue': row[2],
                'chiffre_affaires': row[3] or 0.0
            }
            rapport['produits'].append(produit)
        
        return rapport
    
    def rapport_clients_top(self, date_debut, date_fin):
        """Génère un rapport des meilleurs clients"""
        query = """
            SELECT 
                cl.nom,
                cl.prenom,
                cl.entreprise,
                COUNT(c.id) as nb_commandes,
                SUM(c.total_ttc) as total_achats
            FROM clients cl
            JOIN commandes c ON cl.id = c.client_id
            WHERE c.date_commande BETWEEN ? AND ?
            AND c.statut NOT IN ('Annulée')
            GROUP BY cl.id, cl.nom, cl.prenom, cl.entreprise
            ORDER BY total_achats DESC
            LIMIT 20
        """
        
        results = self.db.execute_query(query, (date_debut, date_fin))
        
        rapport = {
            'periode': f"{date_debut} - {date_fin}",
            'clients': []
        }
        
        for row in results:
            client = {
                'nom': f"{row[1]} {row[0]}",
                'entreprise': row[2] or '',
                'nb_commandes': row[3],
                'total_achats': row[4] or 0.0
            }
            rapport['clients'].append(client)
        
        return rapport
    
    def rapport_stock_faible(self):
        """Génère un rapport des produits avec un stock faible"""
        query = """
            SELECT 
                nom,
                code_produit,
                stock_actuel,
                stock_minimum,
                prix_unitaire
            FROM produits
            WHERE stock_actuel <= stock_minimum
            AND actif = 1
            ORDER BY (stock_minimum - stock_actuel) DESC
        """
        
        results = self.db.execute_query(query)
        
        rapport = {
            'date_generation': datetime.now().strftime("%d/%m/%Y %H:%M"),
            'produits': []
        }
        
        for row in results:
            produit = {
                'nom': row[0],
                'code': row[1] or '',
                'stock_actuel': row[2],
                'stock_minimum': row[3],
                'prix_unitaire': row[4],
                'manque': row[3] - row[2]
            }
            rapport['produits'].append(produit)
        
        return rapport
    
    def rapport_factures_impayees(self):
        """Génère un rapport des factures impayées"""
        query = """
            SELECT 
                f.numero_facture,
                f.date_facture,
                f.date_echeance,
                f.total_ttc,
                cl.nom,
                cl.prenom,
                cl.entreprise
            FROM factures f
            JOIN clients cl ON f.client_id = cl.id
            WHERE f.statut = 'Non payée'
            ORDER BY f.date_echeance
        """
        
        results = self.db.execute_query(query)
        
        rapport = {
            'date_generation': datetime.now().strftime("%d/%m/%Y %H:%M"),
            'factures': [],
            'total_impaye': 0.0
        }
        
        for row in results:
            # Vérifier si en retard
            try:
                if isinstance(row[2], str):
                    date_echeance = datetime.fromisoformat(row[2].replace('Z', '+00:00'))
                else:
                    date_echeance = row[2]
                en_retard = datetime.now() > date_echeance
            except:
                en_retard = False
            
            facture = {
                'numero': row[0],
                'date_facture': row[1],
                'date_echeance': row[2],
                'montant': row[3] or 0.0,
                'client': f"{row[5]} {row[4]}",
                'entreprise': row[6] or '',
                'en_retard': en_retard
            }
            rapport['factures'].append(facture)
            rapport['total_impaye'] += facture['montant']
        
        return rapport
    
    def statistiques_generales(self):
        """Génère des statistiques générales"""
        stats = {}
        
        # Nombre total de clients
        result = self.db.execute_query("SELECT COUNT(*) FROM clients WHERE actif=1")
        stats['nb_clients'] = result[0][0]
        
        # Nombre total de produits
        result = self.db.execute_query("SELECT COUNT(*) FROM produits WHERE actif=1")
        stats['nb_produits'] = result[0][0]
        
        # Commandes du mois
        result = self.db.execute_query("""
            SELECT COUNT(*), COALESCE(SUM(total_ttc), 0)
            FROM commandes 
            WHERE date_commande >= date('now', 'start of month')
            AND statut NOT IN ('Annulée')
        """)
        stats['commandes_mois'] = result[0][0]
        stats['ca_mois'] = result[0][1]
        
        # Commandes de l'année
        result = self.db.execute_query("""
            SELECT COUNT(*), COALESCE(SUM(total_ttc), 0)
            FROM commandes 
            WHERE date_commande >= date('now', 'start of year')
            AND statut NOT IN ('Annulée')
        """)
        stats['commandes_annee'] = result[0][0]
        stats['ca_annee'] = result[0][1]
        
        # Produits en rupture
        result = self.db.execute_query("""
            SELECT COUNT(*) 
            FROM produits 
            WHERE stock_actuel <= stock_minimum AND actif=1
        """)
        stats['produits_rupture'] = result[0][0]
        
        # Factures impayées
        result = self.db.execute_query("""
            SELECT COUNT(*), COALESCE(SUM(total_ttc), 0)
            FROM factures 
            WHERE statut = 'Non payée'
        """)
        stats['factures_impayees'] = result[0][0]
        stats['montant_impaye'] = result[0][1]
        
        return stats
    
    def exporter_rapport_csv(self, rapport, nom_fichier):
        """Exporte un rapport au format CSV"""
        import csv
        import os
        
        # Créer le répertoire exports s'il n'existe pas
        exports_dir = "exports"
        if not os.path.exists(exports_dir):
            os.makedirs(exports_dir)
        
        chemin_fichier = os.path.join(exports_dir, nom_fichier)
        
        try:
            with open(chemin_fichier, 'w', newline='', encoding='utf-8') as csvfile:
                if 'details' in rapport:  # Rapport de ventes
                    writer = csv.writer(csvfile, delimiter=';')
                    writer.writerow(['Date', 'Nb Commandes', 'Total HT', 'Total TTC'])
                    for detail in rapport['details']:
                        writer.writerow([
                            detail['date'],
                            detail['nb_commandes'],
                            f"{detail['total_ht']:.2f}",
                            f"{detail['total_ttc']:.2f}"
                        ])
                
                elif 'produits' in rapport and 'quantite_vendue' in rapport['produits'][0]:  # Produits vendus
                    writer = csv.writer(csvfile, delimiter=';')
                    writer.writerow(['Produit', 'Code', 'Quantité Vendue', 'Chiffre d\'Affaires'])
                    for produit in rapport['produits']:
                        writer.writerow([
                            produit['nom'],
                            produit['code'],
                            produit['quantite_vendue'],
                            f"{produit['chiffre_affaires']:.2f}"
                        ])
                
                elif 'clients' in rapport:  # Top clients
                    writer = csv.writer(csvfile, delimiter=';')
                    writer.writerow(['Client', 'Entreprise', 'Nb Commandes', 'Total Achats'])
                    for client in rapport['clients']:
                        writer.writerow([
                            client['nom'],
                            client['entreprise'],
                            client['nb_commandes'],
                            f"{client['total_achats']:.2f}"
                        ])
            
            return chemin_fichier
            
        except Exception as e:
            raise Exception(f"Erreur lors de l'export CSV: {e}")
