#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test du formulaire client KivyMD
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent / "src"))

from kivymd.app import MD<PERSON><PERSON>
from kivymd.uix.screenmanager import MDScreenManager
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDRaisedButton, MDFlatButton
from kivymd.uix.label import MD<PERSON>abel
from kivymd.uix.dialog import MDDialog

from kivy.metrics import dp

# Import du formulaire client
try:
    from kivymd_forms import ClientFormScreen
    FORM_AVAILABLE = True
except ImportError as e:
    print(f"Erreur d'import du formulaire: {e}")
    FORM_AVAILABLE = False

class TestClientFormScreen(MDScreen):
    """Écran de test pour le formulaire client"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = "test_main"
        self.build_screen()
    
    def build_screen(self):
        """Construit l'écran de test"""
        layout = MDBoxLayout(
            orientation="vertical",
            spacing=dp(20),
            padding=dp(20)
        )
        
        # Titre
        title = MDLabel(
            text="Test Formulaire Client KivyMD",
            font_style="H4",
            halign="center",
            size_hint_y=None,
            height=dp(60)
        )
        layout.add_widget(title)
        
        # Statut
        if FORM_AVAILABLE:
            status_text = "✓ Formulaire client disponible"
            status_color = "Primary"
        else:
            status_text = "✗ Formulaire client non disponible"
            status_color = "Error"
        
        status_label = MDLabel(
            text=status_text,
            theme_text_color=status_color,
            halign="center",
            size_hint_y=None,
            height=dp(40)
        )
        layout.add_widget(status_label)
        
        # Boutons de test
        if FORM_AVAILABLE:
            # Bouton pour ouvrir le formulaire
            open_form_btn = MDRaisedButton(
                text="Ouvrir Formulaire Client",
                size_hint=(None, None),
                size=(dp(250), dp(50)),
                pos_hint={"center_x": 0.5},
                on_release=self.open_client_form
            )
            layout.add_widget(open_form_btn)
            
            # Bouton pour tester avec données
            test_form_btn = MDRaisedButton(
                text="Test avec Données Exemple",
                size_hint=(None, None),
                size=(dp(250), dp(50)),
                pos_hint={"center_x": 0.5},
                on_release=self.test_with_data
            )
            layout.add_widget(test_form_btn)
        else:
            # Message d'erreur
            error_label = MDLabel(
                text="Le formulaire client n'est pas disponible.\nVérifiez que kivymd_forms.py existe.",
                theme_text_color="Error",
                halign="center",
                size_hint_y=None,
                height=dp(80)
            )
            layout.add_widget(error_label)
        
        # Informations
        info_label = MDLabel(
            text="Ce test vérifie que le formulaire client\npeut être ouvert depuis le dashboard.",
            theme_text_color="Secondary",
            halign="center",
            size_hint_y=None,
            height=dp(60)
        )
        layout.add_widget(info_label)
        
        # Statut des actions
        self.action_status = MDLabel(
            text="Aucune action effectuée",
            theme_text_color="Secondary",
            halign="center",
            size_hint_y=None,
            height=dp(40)
        )
        layout.add_widget(self.action_status)
        
        self.add_widget(layout)
    
    def open_client_form(self, *args):
        """Ouvre le formulaire client"""
        try:
            print("Tentative d'ouverture du formulaire client...")
            self.action_status.text = "Ouverture du formulaire..."
            
            app = MDApp.get_running_app()
            
            # Créer le formulaire
            client_form = ClientFormScreen(callback=self.on_form_closed)
            
            # Ajouter au screen manager
            if app.screen_manager.has_screen("client_form"):
                app.screen_manager.remove_widget(app.screen_manager.get_screen("client_form"))
            
            app.screen_manager.add_widget(client_form)
            
            # Naviguer vers le formulaire
            app.screen_manager.current = "client_form"
            
            self.action_status.text = "✓ Formulaire ouvert avec succès"
            print("Formulaire client ouvert avec succès")
            
        except Exception as e:
            error_msg = f"Erreur: {e}"
            print(f"Erreur lors de l'ouverture du formulaire: {e}")
            self.action_status.text = f"✗ Erreur: {e}"
            self.show_error(error_msg)
    
    def test_with_data(self, *args):
        """Test avec des données d'exemple"""
        try:
            print("Test avec données d'exemple...")
            self.action_status.text = "Test avec données..."
            
            # Créer un client fictif pour test
            class FakeClient:
                def __init__(self):
                    self.prenom = "Jean"
                    self.nom = "Dupont"
                    self.email = "<EMAIL>"
                    self.telephone = "0123456789"
                    self.entreprise = "ACME Corp"
                    self.siret = "12345678901234"
                    self.adresse = "123 Rue de la Paix"
                    self.code_postal = "75001"
                    self.ville = "Paris"
                    self.actif = True
            
            fake_client = FakeClient()
            
            app = MDApp.get_running_app()
            
            # Créer le formulaire avec données
            client_form = ClientFormScreen(client=fake_client, callback=self.on_form_closed)
            
            # Ajouter au screen manager
            if app.screen_manager.has_screen("client_form"):
                app.screen_manager.remove_widget(app.screen_manager.get_screen("client_form"))
            
            app.screen_manager.add_widget(client_form)
            
            # Naviguer vers le formulaire
            app.screen_manager.current = "client_form"
            
            self.action_status.text = "✓ Test avec données réussi"
            print("Test avec données d'exemple réussi")
            
        except Exception as e:
            error_msg = f"Erreur: {e}"
            print(f"Erreur lors du test: {e}")
            self.action_status.text = f"✗ Erreur test: {e}"
            self.show_error(error_msg)
    
    def on_form_closed(self):
        """Appelé quand le formulaire est fermé"""
        print("Formulaire fermé, retour à l'écran de test")
        self.action_status.text = "✓ Formulaire fermé, retour effectué"
        
        # Retourner à l'écran de test
        app = MDApp.get_running_app()
        app.screen_manager.current = "test_main"
    
    def show_error(self, message):
        """Affiche un message d'erreur"""
        dialog = MDDialog(
            title="Erreur",
            text=message,
            buttons=[
                MDFlatButton(
                    text="OK",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()

class TestClientFormApp(MDApp):
    """Application de test du formulaire client"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = "Test Formulaire Client KivyMD"
        self.theme_cls.primary_palette = "Blue"
        self.theme_cls.theme_style = "Light"
    
    def build(self):
        """Construit l'application de test"""
        # Initialiser la base de données si possible
        try:
            from src.database.database import DatabaseManager
            self.db = DatabaseManager()
            print("✓ Base de données initialisée")
        except Exception as e:
            print(f"⚠ Base de données non disponible: {e}")
        
        # Gestionnaire d'écrans
        self.screen_manager = MDScreenManager()
        
        # Ajouter l'écran de test
        test_screen = TestClientFormScreen()
        self.screen_manager.add_widget(test_screen)
        
        return self.screen_manager

def main():
    """Point d'entrée"""
    try:
        print("Démarrage du test du formulaire client...")
        app = TestClientFormApp()
        app.run()
    except Exception as e:
        print(f"Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
