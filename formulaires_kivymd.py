#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Formulaires KivyMD pour l'application de gestion commerciale
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent / "src"))

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.textfield import MDTextField
from kivymd.uix.button import MDRaisedButton, MDFlatButton, MDIconButton
from kivymd.uix.label import MDLabel
from kivymd.uix.card import MDCard
from kivymd.uix.dialog import MDDialog
from kivymd.uix.selectioncontrol import MDSwitch
from kivymd.uix.toolbar import MDTopAppBar

from kivy.metrics import dp
from kivy.clock import Clock

# Imports des modèles
try:
    from src.models.client import Client
    from src.models.produit import Produit
    from src.models.commande import Commande
    from src.database.database import DatabaseManager
    MODELS_AVAILABLE = True
except ImportError as e:
    print(f"⚠ Modèles non disponibles: {e}")
    MODELS_AVAILABLE = False

class ClientFormScreen(MDScreen):
    """Formulaire pour créer/modifier un client"""
    
    def __init__(self, client=None, callback=None, **kwargs):
        super().__init__(**kwargs)
        self.name = "client_form"
        self.client = client
        self.callback = callback
        self.build_form()
    
    def build_form(self):
        """Construit le formulaire client"""
        # Layout principal
        main_layout = MDBoxLayout(orientation="vertical")
        
        # Barre de titre
        toolbar = MDTopAppBar(
            title="Nouveau Client" if not self.client else "Modifier Client",
            left_action_items=[["arrow-left", lambda x: self.go_back()]],
            right_action_items=[["content-save", lambda x: self.save_client()]]
        )
        main_layout.add_widget(toolbar)
        
        # Contenu scrollable
        scroll = MDScrollView()
        content = MDBoxLayout(orientation="vertical", spacing=dp(15), padding=dp(20))
        content.bind(minimum_height=content.setter('height'))
        
        # Titre de section
        title = MDLabel(
            text="Informations personnelles",
            font_style="H6",
            theme_text_color="Primary",
            size_hint_y=None,
            height=dp(40)
        )
        content.add_widget(title)
        
        # Champs du formulaire
        self.prenom_field = MDTextField(
            hint_text="Prénom *",
            required=True,
            helper_text="Prénom du client",
            helper_text_mode="on_focus"
        )
        content.add_widget(self.prenom_field)
        
        self.nom_field = MDTextField(
            hint_text="Nom *",
            required=True,
            helper_text="Nom de famille du client",
            helper_text_mode="on_focus"
        )
        content.add_widget(self.nom_field)
        
        self.email_field = MDTextField(
            hint_text="Email",
            helper_text="Adresse email du client",
            helper_text_mode="on_focus"
        )
        content.add_widget(self.email_field)
        
        self.telephone_field = MDTextField(
            hint_text="Téléphone",
            helper_text="Numéro de téléphone",
            helper_text_mode="on_focus"
        )
        content.add_widget(self.telephone_field)
        
        # Section entreprise
        entreprise_title = MDLabel(
            text="Informations entreprise",
            font_style="H6",
            theme_text_color="Primary",
            size_hint_y=None,
            height=dp(40)
        )
        content.add_widget(entreprise_title)
        
        self.entreprise_field = MDTextField(
            hint_text="Entreprise",
            helper_text="Nom de l'entreprise (optionnel)",
            helper_text_mode="on_focus"
        )
        content.add_widget(self.entreprise_field)
        
        self.siret_field = MDTextField(
            hint_text="SIRET",
            helper_text="Numéro SIRET (optionnel)",
            helper_text_mode="on_focus"
        )
        content.add_widget(self.siret_field)
        
        # Section adresse
        adresse_title = MDLabel(
            text="Adresse",
            font_style="H6",
            theme_text_color="Primary",
            size_hint_y=None,
            height=dp(40)
        )
        content.add_widget(adresse_title)
        
        self.adresse_field = MDTextField(
            hint_text="Adresse",
            helper_text="Adresse complète",
            helper_text_mode="on_focus",
            multiline=True,
            max_text_length=200
        )
        content.add_widget(self.adresse_field)
        
        # Grille pour ville et code postal
        ville_layout = MDGridLayout(cols=2, spacing=dp(10), size_hint_y=None, height=dp(60))
        
        self.code_postal_field = MDTextField(
            hint_text="Code postal",
            helper_text="Code postal",
            helper_text_mode="on_focus"
        )
        ville_layout.add_widget(self.code_postal_field)
        
        self.ville_field = MDTextField(
            hint_text="Ville",
            helper_text="Ville",
            helper_text_mode="on_focus"
        )
        ville_layout.add_widget(self.ville_field)
        
        content.add_widget(ville_layout)
        
        # Switch actif
        switch_layout = MDBoxLayout(
            orientation="horizontal",
            spacing=dp(10),
            size_hint_y=None,
            height=dp(50),
            adaptive_height=True
        )
        
        switch_label = MDLabel(
            text="Client actif",
            theme_text_color="Primary",
            size_hint_x=0.8
        )
        switch_layout.add_widget(switch_label)
        
        self.actif_switch = MDSwitch(
            size_hint_x=0.2,
            active=True
        )
        switch_layout.add_widget(self.actif_switch)
        
        content.add_widget(switch_layout)
        
        # Boutons d'action
        buttons_layout = MDBoxLayout(
            orientation="horizontal",
            spacing=dp(10),
            size_hint_y=None,
            height=dp(60),
            padding=[0, dp(20), 0, 0]
        )
        
        cancel_btn = MDFlatButton(
            text="Annuler",
            on_release=self.go_back
        )
        buttons_layout.add_widget(cancel_btn)
        
        save_btn = MDRaisedButton(
            text="Enregistrer",
            on_release=self.save_client
        )
        buttons_layout.add_widget(save_btn)
        
        content.add_widget(buttons_layout)
        
        # Remplir les champs si modification
        if self.client:
            self.fill_form()
        
        scroll.add_widget(content)
        main_layout.add_widget(scroll)
        self.add_widget(main_layout)
    
    def fill_form(self):
        """Remplit le formulaire avec les données du client"""
        if not self.client:
            return
            
        self.prenom_field.text = str(self.client.prenom) if hasattr(self.client, 'prenom') and self.client.prenom else ""
        self.nom_field.text = str(self.client.nom) if hasattr(self.client, 'nom') and self.client.nom else ""
        self.email_field.text = str(self.client.email) if hasattr(self.client, 'email') and self.client.email else ""
        self.telephone_field.text = str(self.client.telephone) if hasattr(self.client, 'telephone') and self.client.telephone else ""
        self.entreprise_field.text = str(self.client.entreprise) if hasattr(self.client, 'entreprise') and self.client.entreprise else ""
        self.siret_field.text = str(self.client.siret) if hasattr(self.client, 'siret') and self.client.siret else ""
        self.adresse_field.text = str(self.client.adresse) if hasattr(self.client, 'adresse') and self.client.adresse else ""
        self.code_postal_field.text = str(self.client.code_postal) if hasattr(self.client, 'code_postal') and self.client.code_postal else ""
        self.ville_field.text = str(self.client.ville) if hasattr(self.client, 'ville') and self.client.ville else ""
        self.actif_switch.active = bool(self.client.actif) if hasattr(self.client, 'actif') else True
    
    def validate_form(self):
        """Valide le formulaire"""
        errors = []
        
        if not self.prenom_field.text.strip():
            errors.append("Le prénom est obligatoire")
            self.prenom_field.error = True
        
        if not self.nom_field.text.strip():
            errors.append("Le nom est obligatoire")
            self.nom_field.error = True
        
        # Validation email si fourni
        if self.email_field.text.strip():
            email = self.email_field.text.strip()
            if "@" not in email or "." not in email:
                errors.append("Format d'email invalide")
                self.email_field.error = True
        
        return errors
    
    def save_client(self, *args):
        """Sauvegarde le client"""
        print("💾 Tentative de sauvegarde du client...")
        
        # Réinitialiser les erreurs
        for field in [self.prenom_field, self.nom_field, self.email_field]:
            field.error = False
        
        # Valider
        errors = self.validate_form()
        if errors:
            self.show_error("\n".join(errors))
            return
        
        try:
            if MODELS_AVAILABLE:
                # Créer ou modifier le client avec les vrais modèles
                if self.client:
                    # Modification
                    self.client.prenom = self.prenom_field.text.strip()
                    self.client.nom = self.nom_field.text.strip()
                    self.client.email = self.email_field.text.strip() or None
                    self.client.telephone = self.telephone_field.text.strip() or None
                    self.client.entreprise = self.entreprise_field.text.strip() or None
                    self.client.siret = self.siret_field.text.strip() or None
                    self.client.adresse = self.adresse_field.text.strip() or None
                    self.client.code_postal = self.code_postal_field.text.strip() or None
                    self.client.ville = self.ville_field.text.strip() or None
                    self.client.actif = self.actif_switch.active
                    self.client.save()
                    message = "Client modifié avec succès"
                else:
                    # Création
                    client_data = {
                        'prenom': self.prenom_field.text.strip(),
                        'nom': self.nom_field.text.strip(),
                        'email': self.email_field.text.strip() or None,
                        'telephone': self.telephone_field.text.strip() or None,
                        'entreprise': self.entreprise_field.text.strip() or None,
                        'siret': self.siret_field.text.strip() or None,
                        'adresse': self.adresse_field.text.strip() or None,
                        'code_postal': self.code_postal_field.text.strip() or None,
                        'ville': self.ville_field.text.strip() or None,
                        'actif': self.actif_switch.active
                    }
                    Client.create(**client_data)
                    message = "Client créé avec succès"
            else:
                # Mode simulation sans base de données
                message = f"✅ Client simulé: {self.prenom_field.text} {self.nom_field.text}"
                print(f"📝 Données client: {self.prenom_field.text} {self.nom_field.text}")
            
            # Callback et retour
            if self.callback:
                self.callback()
            
            self.show_success(message)
            Clock.schedule_once(lambda dt: self.go_back(), 1.5)
            
        except Exception as e:
            self.show_error(f"Erreur lors de la sauvegarde: {e}")
    
    def show_error(self, message):
        """Affiche un message d'erreur"""
        dialog = MDDialog(
            title="Erreur",
            text=message,
            buttons=[
                MDFlatButton(
                    text="OK",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()
    
    def show_success(self, message):
        """Affiche un message de succès"""
        dialog = MDDialog(
            title="Succès",
            text=message,
            buttons=[
                MDFlatButton(
                    text="OK",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()
    
    def go_back(self, *args):
        """Retour à l'écran précédent"""
        print("🔙 Retour depuis le formulaire client")
        if self.callback:
            self.callback()
        # Navigation vers l'écran dashboard
        app = MDApp.get_running_app()
        if hasattr(app, 'screen_manager'):
            app.screen_manager.current = "dashboard"

class ProduitFormScreen(MDScreen):
    """Formulaire pour créer/modifier un produit"""

    def __init__(self, produit=None, callback=None, **kwargs):
        super().__init__(**kwargs)
        self.name = "produit_form"
        self.produit = produit
        self.callback = callback
        self.build_form()

    def build_form(self):
        """Construit le formulaire produit"""
        # Layout principal
        main_layout = MDBoxLayout(orientation="vertical")

        # Barre de titre
        toolbar = MDTopAppBar(
            title="Nouveau Produit" if not self.produit else "Modifier Produit",
            left_action_items=[["arrow-left", lambda x: self.go_back()]],
            right_action_items=[["content-save", lambda x: self.save_produit()]]
        )
        main_layout.add_widget(toolbar)

        # Contenu scrollable
        scroll = MDScrollView()
        content = MDBoxLayout(orientation="vertical", spacing=dp(15), padding=dp(20))
        content.bind(minimum_height=content.setter('height'))

        # Titre de section
        title = MDLabel(
            text="Informations produit",
            font_style="H6",
            theme_text_color="Primary",
            size_hint_y=None,
            height=dp(40)
        )
        content.add_widget(title)

        # Champs du formulaire
        self.nom_field = MDTextField(
            hint_text="Nom du produit *",
            required=True,
            helper_text="Nom du produit",
            helper_text_mode="on_focus"
        )
        content.add_widget(self.nom_field)

        self.code_field = MDTextField(
            hint_text="Code produit",
            helper_text="Code unique du produit (optionnel)",
            helper_text_mode="on_focus"
        )
        content.add_widget(self.code_field)

        self.description_field = MDTextField(
            hint_text="Description",
            helper_text="Description détaillée du produit",
            helper_text_mode="on_focus",
            multiline=True,
            max_text_length=500
        )
        content.add_widget(self.description_field)

        # Section prix et stock
        prix_title = MDLabel(
            text="Prix et stock",
            font_style="H6",
            theme_text_color="Primary",
            size_hint_y=None,
            height=dp(40)
        )
        content.add_widget(prix_title)

        # Grille pour prix
        prix_layout = MDGridLayout(cols=2, spacing=dp(10), size_hint_y=None, height=dp(60))

        self.prix_field = MDTextField(
            hint_text="Prix unitaire *",
            required=True,
            helper_text="Prix en euros",
            helper_text_mode="on_focus",
            input_filter="float"
        )
        prix_layout.add_widget(self.prix_field)

        self.tva_field = MDTextField(
            hint_text="TVA (%)",
            helper_text="Taux de TVA",
            helper_text_mode="on_focus",
            input_filter="float",
            text="20.0"
        )
        prix_layout.add_widget(self.tva_field)

        content.add_widget(prix_layout)

        # Grille pour stock
        stock_layout = MDGridLayout(cols=2, spacing=dp(10), size_hint_y=None, height=dp(60))

        self.stock_actuel_field = MDTextField(
            hint_text="Stock actuel *",
            required=True,
            helper_text="Quantité en stock",
            helper_text_mode="on_focus",
            input_filter="int"
        )
        stock_layout.add_widget(self.stock_actuel_field)

        self.stock_minimum_field = MDTextField(
            hint_text="Stock minimum",
            helper_text="Seuil d'alerte",
            helper_text_mode="on_focus",
            input_filter="int",
            text="5"
        )
        stock_layout.add_widget(self.stock_minimum_field)

        content.add_widget(stock_layout)

        # Switch actif
        switch_layout = MDBoxLayout(
            orientation="horizontal",
            spacing=dp(10),
            size_hint_y=None,
            height=dp(50),
            adaptive_height=True
        )

        switch_label = MDLabel(
            text="Produit actif",
            theme_text_color="Primary",
            size_hint_x=0.8
        )
        switch_layout.add_widget(switch_label)

        self.actif_switch = MDSwitch(
            size_hint_x=0.2,
            active=True
        )
        switch_layout.add_widget(self.actif_switch)

        content.add_widget(switch_layout)

        # Boutons d'action
        buttons_layout = MDBoxLayout(
            orientation="horizontal",
            spacing=dp(10),
            size_hint_y=None,
            height=dp(60),
            padding=[0, dp(20), 0, 0]
        )

        cancel_btn = MDFlatButton(
            text="Annuler",
            on_release=self.go_back
        )
        buttons_layout.add_widget(cancel_btn)

        save_btn = MDRaisedButton(
            text="Enregistrer",
            on_release=self.save_produit
        )
        buttons_layout.add_widget(save_btn)

        content.add_widget(buttons_layout)

        # Remplir les champs si modification
        if self.produit:
            self.fill_form()

        scroll.add_widget(content)
        main_layout.add_widget(scroll)
        self.add_widget(main_layout)

    def fill_form(self):
        """Remplit le formulaire avec les données du produit"""
        if not self.produit:
            return

        self.nom_field.text = str(self.produit.nom) if hasattr(self.produit, 'nom') and self.produit.nom else ""
        self.code_field.text = str(self.produit.code_produit) if hasattr(self.produit, 'code_produit') and self.produit.code_produit else ""
        self.description_field.text = str(self.produit.description) if hasattr(self.produit, 'description') and self.produit.description else ""
        self.prix_field.text = str(self.produit.prix_unitaire) if hasattr(self.produit, 'prix_unitaire') and self.produit.prix_unitaire else ""
        self.tva_field.text = str(self.produit.taux_tva) if hasattr(self.produit, 'taux_tva') else "20.0"
        self.stock_actuel_field.text = str(self.produit.stock_actuel) if hasattr(self.produit, 'stock_actuel') and self.produit.stock_actuel is not None else ""
        self.stock_minimum_field.text = str(self.produit.stock_minimum) if hasattr(self.produit, 'stock_minimum') else "5"
        self.actif_switch.active = bool(self.produit.actif) if hasattr(self.produit, 'actif') else True

    def validate_form(self):
        """Valide le formulaire"""
        errors = []

        if not self.nom_field.text.strip():
            errors.append("Le nom du produit est obligatoire")
            self.nom_field.error = True

        if not self.prix_field.text.strip():
            errors.append("Le prix est obligatoire")
            self.prix_field.error = True
        else:
            try:
                prix = float(self.prix_field.text.strip())
                if prix <= 0:
                    errors.append("Le prix doit être positif")
                    self.prix_field.error = True
            except ValueError:
                errors.append("Le prix doit être un nombre valide")
                self.prix_field.error = True

        if not self.stock_actuel_field.text.strip():
            errors.append("Le stock actuel est obligatoire")
            self.stock_actuel_field.error = True
        else:
            try:
                stock = int(self.stock_actuel_field.text.strip())
                if stock < 0:
                    errors.append("Le stock ne peut pas être négatif")
                    self.stock_actuel_field.error = True
            except ValueError:
                errors.append("Le stock doit être un nombre entier")
                self.stock_actuel_field.error = True

        # Validation TVA
        if self.tva_field.text.strip():
            try:
                tva = float(self.tva_field.text.strip())
                if tva < 0 or tva > 100:
                    errors.append("La TVA doit être entre 0 et 100%")
                    self.tva_field.error = True
            except ValueError:
                errors.append("La TVA doit être un nombre valide")
                self.tva_field.error = True

        return errors

    def save_produit(self, *args):
        """Sauvegarde le produit"""
        print("💾 Tentative de sauvegarde du produit...")

        # Réinitialiser les erreurs
        for field in [self.nom_field, self.prix_field, self.stock_actuel_field, self.tva_field]:
            field.error = False

        # Valider
        errors = self.validate_form()
        if errors:
            self.show_error("\n".join(errors))
            return

        try:
            # Préparer les données
            prix = float(self.prix_field.text.strip())
            stock_actuel = int(self.stock_actuel_field.text.strip())
            tva = float(self.tva_field.text.strip()) if self.tva_field.text.strip() else 20.0
            stock_minimum = int(self.stock_minimum_field.text.strip()) if self.stock_minimum_field.text.strip() else 5

            if MODELS_AVAILABLE:
                if self.produit:
                    # Modification
                    self.produit.nom = self.nom_field.text.strip()
                    self.produit.code_produit = self.code_field.text.strip() or None
                    self.produit.description = self.description_field.text.strip() or None
                    self.produit.prix_unitaire = prix
                    self.produit.taux_tva = tva
                    self.produit.stock_actuel = stock_actuel
                    self.produit.stock_minimum = stock_minimum
                    self.produit.actif = self.actif_switch.active
                    self.produit.save()
                    message = "Produit modifié avec succès"
                else:
                    # Création
                    produit_data = {
                        'nom': self.nom_field.text.strip(),
                        'code_produit': self.code_field.text.strip() or None,
                        'description': self.description_field.text.strip() or None,
                        'prix_unitaire': prix,
                        'taux_tva': tva,
                        'stock_actuel': stock_actuel,
                        'stock_minimum': stock_minimum,
                        'actif': self.actif_switch.active
                    }
                    Produit.create(**produit_data)
                    message = "Produit créé avec succès"
            else:
                # Mode simulation
                message = f"✅ Produit simulé: {self.nom_field.text} - {prix}€"
                print(f"📝 Données produit: {self.nom_field.text} - {prix}€ - Stock: {stock_actuel}")

            # Callback et retour
            if self.callback:
                self.callback()

            self.show_success(message)
            Clock.schedule_once(lambda dt: self.go_back(), 1.5)

        except Exception as e:
            self.show_error(f"Erreur lors de la sauvegarde: {e}")

    def show_error(self, message):
        """Affiche un message d'erreur"""
        dialog = MDDialog(
            title="Erreur",
            text=message,
            buttons=[
                MDFlatButton(
                    text="OK",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()

    def show_success(self, message):
        """Affiche un message de succès"""
        dialog = MDDialog(
            title="Succès",
            text=message,
            buttons=[
                MDFlatButton(
                    text="OK",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()

    def go_back(self, *args):
        """Retour à l'écran précédent"""
        print("🔙 Retour depuis le formulaire produit")
        if self.callback:
            self.callback()
        # Navigation vers l'écran dashboard
        app = MDApp.get_running_app()
        if hasattr(app, 'screen_manager'):
            app.screen_manager.current = "dashboard"
