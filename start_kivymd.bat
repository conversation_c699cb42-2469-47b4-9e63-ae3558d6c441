@echo off
echo Démarrage de l'application de Gestion Commerciale (KivyMD)...
echo.

REM Vérifier si Python est installé
python --version >nul 2>&1
if errorlevel 1 (
    echo ERREUR: Python n'est pas installé ou n'est pas dans le PATH
    echo Veuillez installer <PERSON> depuis https://python.org
    pause
    exit /b 1
)

REM Créer le répertoire data s'il n'existe pas
if not exist "data" mkdir data

REM Créer le répertoire assets s'il n'existe pas
if not exist "assets" mkdir assets

REM Lancer l'application KivyMD
echo Lancement de l'application KivyMD...
python app_kivymd.py

REM Si l'application se ferme avec une erreur, afficher un message
if errorlevel 1 (
    echo.
    echo L'application s'est fermée avec une erreur.
    echo.
    echo Si KivyMD n'est pas installé, exécutez : install_kivymd.bat
    echo Sinon, vérifiez les messages d'erreur ci-dessus.
    pause
)
