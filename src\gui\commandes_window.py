#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fenêtre de gestion des commandes
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os
from datetime import datetime

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from src.models.commande import Commande
from src.models.client import Client
from src.models.produit import Produit

class CommandesWindow:
    """Fenêtre de gestion des commandes"""
    
    def __init__(self, parent):
        self.window = tk.Toplevel(parent)
        self.window.title("Gestion des Commandes")
        self.window.geometry("1200x700")
        self.window.transient(parent)
        self.window.grab_set()
        
        self.selected_commande = None
        self.setup_ui()
        self.load_commandes()
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        # Frame principal
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configuration du redimensionnement
        self.window.columnconfigure(0, weight=1)
        self.window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # Titre
        title_label = ttk.Label(main_frame, text="Gestion des Commandes", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # Frame pour les boutons d'action
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N), padx=(0, 10))
        
        # Boutons d'action
        ttk.Button(buttons_frame, text="Nouvelle Commande", 
                  command=self.new_commande).grid(row=0, column=0, pady=5, sticky=(tk.W, tk.E))
        ttk.Button(buttons_frame, text="Voir Détails", 
                  command=self.view_commande).grid(row=1, column=0, pady=5, sticky=(tk.W, tk.E))
        ttk.Button(buttons_frame, text="Modifier", 
                  command=self.edit_commande).grid(row=2, column=0, pady=5, sticky=(tk.W, tk.E))
        ttk.Button(buttons_frame, text="Changer Statut", 
                  command=self.change_status).grid(row=3, column=0, pady=5, sticky=(tk.W, tk.E))
        
        ttk.Separator(buttons_frame, orient=tk.HORIZONTAL).grid(row=4, column=0, pady=10, sticky=(tk.W, tk.E))
        
        # Filtres par statut
        ttk.Label(buttons_frame, text="Filtrer par statut:", font=("Arial", 10, "bold")).grid(row=5, column=0, sticky=tk.W, pady=(0, 5))
        
        self.status_var = tk.StringVar(value="Tous")
        status_values = ["Tous", "En attente", "Confirmée", "Expédiée", "Livrée", "Annulée"]
        status_combo = ttk.Combobox(buttons_frame, textvariable=self.status_var, 
                                   values=status_values, state="readonly", width=18)
        status_combo.grid(row=6, column=0, pady=(0, 5), sticky=(tk.W, tk.E))
        status_combo.bind("<<ComboboxSelected>>", self.filter_by_status)
        
        ttk.Button(buttons_frame, text="Actualiser", 
                  command=self.load_commandes).grid(row=7, column=0, pady=10, sticky=(tk.W, tk.E))
        
        buttons_frame.columnconfigure(0, weight=1)
        
        # Frame pour la liste des commandes
        list_frame = ttk.LabelFrame(main_frame, text="Liste des commandes", padding="10")
        list_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        # Treeview pour afficher les commandes
        columns = ("ID", "Numéro", "Client", "Date", "Statut", "Total HT", "Total TTC")
        self.tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)
        
        # Configuration des colonnes
        column_widths = {"ID": 50, "Numéro": 120, "Client": 200, "Date": 100, 
                        "Statut": 100, "Total HT": 100, "Total TTC": 100}
        
        for col in columns:
            self.tree.heading(col, text=col, command=lambda c=col: self.sort_treeview(c))
            self.tree.column(col, width=column_widths.get(col, 100))
        
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.tree.yview)
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.tree.configure(yscrollcommand=v_scrollbar.set)
        
        h_scrollbar = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        self.tree.configure(xscrollcommand=h_scrollbar.set)
        
        # Bind double-click pour voir les détails
        self.tree.bind("<Double-1>", lambda e: self.view_commande())
        self.tree.bind("<<TreeviewSelect>>", self.on_select_commande)
    
    def load_commandes(self):
        """Charge la liste des commandes"""
        try:
            # Vider le treeview
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # Charger les commandes
            commandes = Commande.get_all()
            
            for commande in commandes:
                # Récupérer le nom du client
                client = Client.get_by_id(commande.client_id)
                client_nom = str(client) if client else "Client inconnu"
                
                # Formater la date
                if isinstance(commande.date_commande, str):
                    try:
                        date_obj = datetime.fromisoformat(commande.date_commande.replace('Z', '+00:00'))
                        date_str = date_obj.strftime("%d/%m/%Y")
                    except:
                        date_str = commande.date_commande
                else:
                    date_str = commande.date_commande.strftime("%d/%m/%Y") if commande.date_commande else ""
                
                # Déterminer la couleur selon le statut
                tags = ()
                if commande.statut == "En attente":
                    tags = ("pending",)
                elif commande.statut == "Confirmée":
                    tags = ("confirmed",)
                elif commande.statut == "Expédiée":
                    tags = ("shipped",)
                elif commande.statut == "Livrée":
                    tags = ("delivered",)
                elif commande.statut == "Annulée":
                    tags = ("cancelled",)
                
                self.tree.insert("", tk.END, values=(
                    commande.id,
                    commande.numero_commande,
                    client_nom,
                    date_str,
                    commande.statut,
                    f"{commande.total_ht:.2f}€",
                    f"{commande.total_ttc:.2f}€"
                ), tags=tags)
            
            # Configuration des tags pour colorer les lignes
            self.tree.tag_configure("pending", background="#fff2cc")
            self.tree.tag_configure("confirmed", background="#d4edda")
            self.tree.tag_configure("shipped", background="#cce5ff")
            self.tree.tag_configure("delivered", background="#d1ecf1")
            self.tree.tag_configure("cancelled", background="#f8d7da")
                
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des commandes: {e}")
    
    def filter_by_status(self, event=None):
        """Filtre les commandes par statut"""
        status = self.status_var.get()
        
        if status == "Tous":
            self.load_commandes()
            return
        
        try:
            # Vider le treeview
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # Charger les commandes filtrées
            commandes = Commande.get_all()
            
            for commande in commandes:
                if commande.statut != status:
                    continue
                
                # Récupérer le nom du client
                client = Client.get_by_id(commande.client_id)
                client_nom = str(client) if client else "Client inconnu"
                
                # Formater la date
                if isinstance(commande.date_commande, str):
                    try:
                        date_obj = datetime.fromisoformat(commande.date_commande.replace('Z', '+00:00'))
                        date_str = date_obj.strftime("%d/%m/%Y")
                    except:
                        date_str = commande.date_commande
                else:
                    date_str = commande.date_commande.strftime("%d/%m/%Y") if commande.date_commande else ""
                
                # Déterminer la couleur selon le statut
                tags = ()
                if commande.statut == "En attente":
                    tags = ("pending",)
                elif commande.statut == "Confirmée":
                    tags = ("confirmed",)
                elif commande.statut == "Expédiée":
                    tags = ("shipped",)
                elif commande.statut == "Livrée":
                    tags = ("delivered",)
                elif commande.statut == "Annulée":
                    tags = ("cancelled",)
                
                self.tree.insert("", tk.END, values=(
                    commande.id,
                    commande.numero_commande,
                    client_nom,
                    date_str,
                    commande.statut,
                    f"{commande.total_ht:.2f}€",
                    f"{commande.total_ttc:.2f}€"
                ), tags=tags)
            
            # Configuration des tags
            self.tree.tag_configure("pending", background="#fff2cc")
            self.tree.tag_configure("confirmed", background="#d4edda")
            self.tree.tag_configure("shipped", background="#cce5ff")
            self.tree.tag_configure("delivered", background="#d1ecf1")
            self.tree.tag_configure("cancelled", background="#f8d7da")
                
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du filtrage: {e}")
    
    def on_select_commande(self, event):
        """Appelé quand une commande est sélectionnée"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            commande_id = item['values'][0]
            self.selected_commande = Commande.get_by_id(commande_id)
    
    def new_commande(self):
        """Ouvre la fenêtre de création d'une nouvelle commande"""
        CommandeFormWindow(self.window, callback=self.load_commandes)
    
    def view_commande(self):
        """Affiche les détails de la commande sélectionnée"""
        if not self.selected_commande:
            messagebox.showwarning("Attention", "Veuillez sélectionner une commande à consulter")
            return
        
        CommandeDetailsWindow(self.window, self.selected_commande)
    
    def edit_commande(self):
        """Ouvre la fenêtre d'édition de la commande sélectionnée"""
        if not self.selected_commande:
            messagebox.showwarning("Attention", "Veuillez sélectionner une commande à modifier")
            return
        
        if self.selected_commande.statut in ["Livrée", "Annulée"]:
            messagebox.showwarning("Attention", "Impossible de modifier une commande livrée ou annulée")
            return
        
        CommandeFormWindow(self.window, commande=self.selected_commande, callback=self.load_commandes)
    
    def change_status(self):
        """Change le statut de la commande sélectionnée"""
        if not self.selected_commande:
            messagebox.showwarning("Attention", "Veuillez sélectionner une commande")
            return
        
        StatusChangeWindow(self.window, self.selected_commande, callback=self.load_commandes)
    
    def sort_treeview(self, col):
        """Trie le treeview par colonne"""
        data = [(self.tree.set(child, col), child) for child in self.tree.get_children('')]
        
        # Tri numérique pour certaines colonnes
        if col in ["ID", "Total HT", "Total TTC"]:
            try:
                data.sort(key=lambda x: float(x[0].replace('€', '').replace(',', '.')))
            except:
                data.sort()
        else:
            data.sort()
        
        for index, (val, child) in enumerate(data):
            self.tree.move(child, '', index)


class StatusChangeWindow:
    """Fenêtre pour changer le statut d'une commande"""

    def __init__(self, parent, commande, callback=None):
        self.window = tk.Toplevel(parent)
        self.window.title("Changer le statut")
        self.window.geometry("300x200")
        self.window.transient(parent)
        self.window.grab_set()

        self.commande = commande
        self.callback = callback

        self.setup_ui()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_frame = ttk.Frame(self.window, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        self.window.columnconfigure(0, weight=1)
        self.window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        # Commande info
        ttk.Label(main_frame, text=f"Commande: {self.commande.numero_commande}").grid(row=0, column=0, columnspan=2, pady=(0, 10))
        ttk.Label(main_frame, text=f"Statut actuel: {self.commande.statut}").grid(row=1, column=0, columnspan=2, pady=(0, 20))

        # Nouveau statut
        ttk.Label(main_frame, text="Nouveau statut:").grid(row=2, column=0, sticky=tk.W, pady=5)

        self.status_var = tk.StringVar(value=self.commande.statut)
        status_values = ["En attente", "Confirmée", "Expédiée", "Livrée", "Annulée"]
        status_combo = ttk.Combobox(main_frame, textvariable=self.status_var,
                                   values=status_values, state="readonly")
        status_combo.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5)

        # Boutons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=3, column=0, columnspan=2, pady=20)

        ttk.Button(buttons_frame, text="Confirmer", command=self.save_status).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(buttons_frame, text="Annuler", command=self.window.destroy).grid(row=0, column=1)

    def save_status(self):
        """Sauvegarde le nouveau statut"""
        try:
            nouveau_statut = self.status_var.get()
            if nouveau_statut != self.commande.statut:
                self.commande.statut = nouveau_statut
                self.commande.save()

                messagebox.showinfo("Succès", "Statut mis à jour avec succès")

                if self.callback:
                    self.callback()

                self.window.destroy()
            else:
                messagebox.showinfo("Info", "Aucun changement de statut")

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la mise à jour: {e}")


class CommandeDetailsWindow:
    """Fenêtre pour afficher les détails d'une commande"""

    def __init__(self, parent, commande):
        self.window = tk.Toplevel(parent)
        self.window.title(f"Détails de la commande {commande.numero_commande}")
        self.window.geometry("800x600")
        self.window.transient(parent)
        self.window.grab_set()

        self.commande = commande
        self.setup_ui()
        self.load_details()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        self.window.columnconfigure(0, weight=1)
        self.window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)

        # Informations générales
        info_frame = ttk.LabelFrame(main_frame, text="Informations générales", padding="10")
        info_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        info_frame.columnconfigure(1, weight=1)
        info_frame.columnconfigure(3, weight=1)

        # Client
        client = Client.get_by_id(self.commande.client_id)
        client_info = str(client) if client else "Client inconnu"

        ttk.Label(info_frame, text="Numéro:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        ttk.Label(info_frame, text=self.commande.numero_commande).grid(row=0, column=1, sticky=tk.W)

        ttk.Label(info_frame, text="Client:").grid(row=0, column=2, sticky=tk.W, padx=(20, 10))
        ttk.Label(info_frame, text=client_info).grid(row=0, column=3, sticky=tk.W)

        # Date et statut
        date_str = self.commande.date_commande.strftime("%d/%m/%Y %H:%M") if self.commande.date_commande else ""

        ttk.Label(info_frame, text="Date:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(5, 0))
        ttk.Label(info_frame, text=date_str).grid(row=1, column=1, sticky=tk.W, pady=(5, 0))

        ttk.Label(info_frame, text="Statut:").grid(row=1, column=2, sticky=tk.W, padx=(20, 10), pady=(5, 0))
        ttk.Label(info_frame, text=self.commande.statut).grid(row=1, column=3, sticky=tk.W, pady=(5, 0))

        # Totaux
        totaux_frame = ttk.LabelFrame(main_frame, text="Totaux", padding="10")
        totaux_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(totaux_frame, text=f"Total HT: {self.commande.total_ht:.2f}€").grid(row=0, column=0, sticky=tk.W, padx=(0, 20))
        ttk.Label(totaux_frame, text=f"TVA ({self.commande.tva}%): {(self.commande.total_ttc - self.commande.total_ht):.2f}€").grid(row=0, column=1, sticky=tk.W, padx=(0, 20))
        ttk.Label(totaux_frame, text=f"Total TTC: {self.commande.total_ttc:.2f}€", font=("Arial", 10, "bold")).grid(row=0, column=2, sticky=tk.W)

        # Lignes de commande
        lignes_frame = ttk.LabelFrame(main_frame, text="Lignes de commande", padding="10")
        lignes_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        lignes_frame.columnconfigure(0, weight=1)
        lignes_frame.rowconfigure(0, weight=1)

        # Treeview pour les lignes
        columns = ("Produit", "Quantité", "Prix unitaire", "Total")
        self.lignes_tree = ttk.Treeview(lignes_frame, columns=columns, show="headings", height=10)

        for col in columns:
            self.lignes_tree.heading(col, text=col)
            if col == "Produit":
                self.lignes_tree.column(col, width=300)
            else:
                self.lignes_tree.column(col, width=100)

        self.lignes_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Scrollbar pour les lignes
        lignes_scrollbar = ttk.Scrollbar(lignes_frame, orient=tk.VERTICAL, command=self.lignes_tree.yview)
        lignes_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.lignes_tree.configure(yscrollcommand=lignes_scrollbar.set)

        # Notes
        if self.commande.notes:
            notes_frame = ttk.LabelFrame(main_frame, text="Notes", padding="10")
            notes_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
            notes_frame.columnconfigure(0, weight=1)

            notes_text = tk.Text(notes_frame, height=3, wrap=tk.WORD, state=tk.DISABLED)
            notes_text.grid(row=0, column=0, sticky=(tk.W, tk.E))
            notes_text.insert(1.0, self.commande.notes)

        # Bouton fermer
        ttk.Button(main_frame, text="Fermer", command=self.window.destroy).grid(row=4, column=0, pady=10)

    def load_details(self):
        """Charge les détails de la commande"""
        try:
            # Charger les lignes de commande
            self.commande.charger_lignes()

            for ligne in self.commande.lignes:
                # Récupérer le nom du produit
                produit = Produit.get_by_id(ligne.produit_id)
                produit_nom = produit.nom if produit else "Produit inconnu"

                self.lignes_tree.insert("", tk.END, values=(
                    produit_nom,
                    ligne.quantite,
                    f"{ligne.prix_unitaire:.2f}€",
                    f"{ligne.total_ligne:.2f}€"
                ))

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des détails: {e}")


class CommandeFormWindow:
    """Fenêtre de formulaire pour créer/modifier une commande"""

    def __init__(self, parent, commande=None, callback=None):
        self.window = tk.Toplevel(parent)
        self.window.title("Nouvelle Commande" if commande is None else "Modifier Commande")
        self.window.geometry("900x700")
        self.window.transient(parent)
        self.window.grab_set()

        self.commande = commande
        self.callback = callback
        self.lignes = []

        self.setup_ui()
        if self.commande:
            self.load_commande_data()

    def setup_ui(self):
        """Configure l'interface utilisateur du formulaire"""
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        self.window.columnconfigure(0, weight=1)
        self.window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)

        # Informations générales
        info_frame = ttk.LabelFrame(main_frame, text="Informations générales", padding="10")
        info_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        info_frame.columnconfigure(1, weight=1)
        info_frame.columnconfigure(3, weight=1)

        # Client
        ttk.Label(info_frame, text="Client *:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.client_var = tk.StringVar()
        self.client_combo = ttk.Combobox(info_frame, textvariable=self.client_var,
                                        state="readonly", width=30)
        self.client_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 20))
        self.load_clients()

        # Statut
        ttk.Label(info_frame, text="Statut:").grid(row=0, column=2, sticky=tk.W, padx=(0, 10))
        self.statut_var = tk.StringVar(value="En attente")
        status_values = ["En attente", "Confirmée", "Expédiée", "Livrée", "Annulée"]
        statut_combo = ttk.Combobox(info_frame, textvariable=self.statut_var,
                                   values=status_values, state="readonly")
        statut_combo.grid(row=0, column=3, sticky=(tk.W, tk.E))

        # TVA
        ttk.Label(info_frame, text="TVA (%):").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        self.tva_var = tk.StringVar(value="20")
        tva_entry = ttk.Entry(info_frame, textvariable=self.tva_var, width=10)
        tva_entry.grid(row=1, column=1, sticky=tk.W, pady=(10, 0))

        # Notes
        ttk.Label(info_frame, text="Notes:").grid(row=2, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        self.notes_text = tk.Text(info_frame, height=3, width=50)
        self.notes_text.grid(row=2, column=1, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))

        # Ajout de produits
        ajout_frame = ttk.LabelFrame(main_frame, text="Ajouter un produit", padding="10")
        ajout_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        ajout_frame.columnconfigure(1, weight=1)

        ttk.Label(ajout_frame, text="Produit:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.produit_var = tk.StringVar()
        self.produit_combo = ttk.Combobox(ajout_frame, textvariable=self.produit_var,
                                         state="readonly", width=30)
        self.produit_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        self.load_produits()

        ttk.Label(ajout_frame, text="Quantité:").grid(row=0, column=2, sticky=tk.W, padx=(0, 10))
        self.quantite_var = tk.StringVar(value="1")
        quantite_entry = ttk.Entry(ajout_frame, textvariable=self.quantite_var, width=10)
        quantite_entry.grid(row=0, column=3, padx=(0, 10))

        ttk.Button(ajout_frame, text="Ajouter", command=self.ajouter_produit).grid(row=0, column=4)

        # Liste des produits
        lignes_frame = ttk.LabelFrame(main_frame, text="Produits commandés", padding="10")
        lignes_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        lignes_frame.columnconfigure(0, weight=1)
        lignes_frame.rowconfigure(0, weight=1)

        # Treeview pour les lignes
        columns = ("Produit", "Quantité", "Prix unitaire", "Total", "Action")
        self.lignes_tree = ttk.Treeview(lignes_frame, columns=columns, show="headings", height=8)

        column_widths = {"Produit": 250, "Quantité": 80, "Prix unitaire": 100, "Total": 100, "Action": 80}
        for col in columns:
            self.lignes_tree.heading(col, text=col)
            self.lignes_tree.column(col, width=column_widths.get(col, 100))

        self.lignes_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Scrollbar
        lignes_scrollbar = ttk.Scrollbar(lignes_frame, orient=tk.VERTICAL, command=self.lignes_tree.yview)
        lignes_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.lignes_tree.configure(yscrollcommand=lignes_scrollbar.set)

        # Bind pour supprimer une ligne
        self.lignes_tree.bind("<Double-1>", self.supprimer_ligne)

        # Totaux
        totaux_frame = ttk.Frame(lignes_frame)
        totaux_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        totaux_frame.columnconfigure(0, weight=1)

        self.total_ht_var = tk.StringVar(value="0.00€")
        self.total_ttc_var = tk.StringVar(value="0.00€")

        ttk.Label(totaux_frame, text="Total HT:", font=("Arial", 10, "bold")).grid(row=0, column=0, sticky=tk.E, padx=(0, 10))
        ttk.Label(totaux_frame, textvariable=self.total_ht_var, font=("Arial", 10, "bold")).grid(row=0, column=1, sticky=tk.E, padx=(0, 20))

        ttk.Label(totaux_frame, text="Total TTC:", font=("Arial", 12, "bold")).grid(row=0, column=2, sticky=tk.E, padx=(0, 10))
        ttk.Label(totaux_frame, textvariable=self.total_ttc_var, font=("Arial", 12, "bold")).grid(row=0, column=3, sticky=tk.E)

        # Boutons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=3, column=0, pady=20)

        ttk.Button(buttons_frame, text="Enregistrer", command=self.save_commande).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(buttons_frame, text="Annuler", command=self.window.destroy).grid(row=0, column=1)

    def load_clients(self):
        """Charge les clients dans le combobox"""
        try:
            clients = Client.get_all()
            client_list = [str(client) for client in clients]
            self.client_combo['values'] = client_list
            self.clients_data = {str(client): client.id for client in clients}
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des clients: {e}")

    def load_produits(self):
        """Charge les produits dans le combobox"""
        try:
            produits = Produit.get_all()
            produit_list = [f"{p.nom} ({p.code_produit}) - {p.prix_unitaire:.2f}€" for p in produits]
            self.produit_combo['values'] = produit_list
            self.produits_data = {f"{p.nom} ({p.code_produit}) - {p.prix_unitaire:.2f}€": p for p in produits}
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des produits: {e}")

    def ajouter_produit(self):
        """Ajoute un produit à la commande"""
        try:
            produit_str = self.produit_var.get()
            if not produit_str:
                messagebox.showwarning("Attention", "Veuillez sélectionner un produit")
                return

            quantite = int(self.quantite_var.get())
            if quantite <= 0:
                messagebox.showerror("Erreur", "La quantité doit être positive")
                return

            produit = self.produits_data[produit_str]

            # Vérifier le stock
            if produit.stock_actuel < quantite:
                if not messagebox.askyesno("Stock insuffisant",
                                         f"Stock disponible: {produit.stock_actuel}\n"
                                         f"Quantité demandée: {quantite}\n\n"
                                         "Continuer quand même ?"):
                    return

            # Ajouter à la liste
            ligne_data = {
                'produit': produit,
                'quantite': quantite,
                'prix_unitaire': produit.prix_unitaire,
                'total': quantite * produit.prix_unitaire
            }
            self.lignes.append(ligne_data)

            # Ajouter au treeview
            self.lignes_tree.insert("", tk.END, values=(
                produit.nom,
                quantite,
                f"{produit.prix_unitaire:.2f}€",
                f"{ligne_data['total']:.2f}€",
                "Supprimer"
            ))

            # Réinitialiser les champs
            self.produit_var.set("")
            self.quantite_var.set("1")

            # Recalculer les totaux
            self.calculer_totaux()

        except ValueError:
            messagebox.showerror("Erreur", "Veuillez saisir une quantité valide")
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'ajout du produit: {e}")

    def supprimer_ligne(self, event):
        """Supprime une ligne de la commande"""
        selection = self.lignes_tree.selection()
        if selection:
            item = self.lignes_tree.item(selection[0])
            index = self.lignes_tree.index(selection[0])

            if messagebox.askyesno("Confirmation", "Supprimer cette ligne ?"):
                # Supprimer de la liste
                del self.lignes[index]

                # Supprimer du treeview
                self.lignes_tree.delete(selection[0])

                # Recalculer les totaux
                self.calculer_totaux()

    def calculer_totaux(self):
        """Calcule et affiche les totaux"""
        try:
            total_ht = sum(ligne['total'] for ligne in self.lignes)
            tva_pct = float(self.tva_var.get())
            total_ttc = total_ht * (1 + tva_pct / 100)

            self.total_ht_var.set(f"{total_ht:.2f}€")
            self.total_ttc_var.set(f"{total_ttc:.2f}€")

        except ValueError:
            self.total_ht_var.set("0.00€")
            self.total_ttc_var.set("0.00€")

    def load_commande_data(self):
        """Charge les données de la commande dans le formulaire"""
        if self.commande:
            # Charger les informations générales
            client = Client.get_by_id(self.commande.client_id)
            if client:
                self.client_var.set(str(client))

            self.statut_var.set(self.commande.statut)
            self.tva_var.set(str(self.commande.tva))

            if self.commande.notes:
                self.notes_text.insert(1.0, self.commande.notes)

            # Charger les lignes
            self.commande.charger_lignes()
            for ligne in self.commande.lignes:
                produit = Produit.get_by_id(ligne.produit_id)
                if produit:
                    ligne_data = {
                        'produit': produit,
                        'quantite': ligne.quantite,
                        'prix_unitaire': ligne.prix_unitaire,
                        'total': ligne.total_ligne
                    }
                    self.lignes.append(ligne_data)

                    self.lignes_tree.insert("", tk.END, values=(
                        produit.nom,
                        ligne.quantite,
                        f"{ligne.prix_unitaire:.2f}€",
                        f"{ligne.total_ligne:.2f}€",
                        "Supprimer"
                    ))

            self.calculer_totaux()

    def save_commande(self):
        """Sauvegarde la commande"""
        try:
            # Validation
            client_str = self.client_var.get()
            if not client_str:
                messagebox.showerror("Erreur", "Veuillez sélectionner un client")
                return

            if not self.lignes:
                messagebox.showerror("Erreur", "Veuillez ajouter au moins un produit")
                return

            try:
                tva = float(self.tva_var.get())
                if tva < 0:
                    raise ValueError()
            except ValueError:
                messagebox.showerror("Erreur", "Le taux de TVA doit être un nombre positif")
                return

            # Créer ou modifier la commande
            if self.commande is None:
                commande = Commande()
            else:
                commande = self.commande

            # Remplir les données
            commande.client_id = self.clients_data[client_str]
            commande.statut = self.statut_var.get()
            commande.tva = tva
            commande.notes = self.notes_text.get(1.0, tk.END).strip()

            # Ajouter les lignes
            commande.lignes = []
            for ligne_data in self.lignes:
                commande.ajouter_ligne(
                    ligne_data['produit'].id,
                    ligne_data['quantite'],
                    ligne_data['prix_unitaire']
                )

            # Sauvegarder
            commande.save()

            messagebox.showinfo("Succès", "Commande enregistrée avec succès")

            # Appeler le callback
            if self.callback:
                self.callback()

            self.window.destroy()

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'enregistrement: {e}")
