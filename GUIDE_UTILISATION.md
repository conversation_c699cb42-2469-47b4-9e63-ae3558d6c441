# Guide d'Utilisation - Application de Gestion Commerciale

## Installation et Démarrage

### Prérequis
- Python 3.7 ou supérieur installé sur votre système
- Windows 10 ou supérieur

### Installation
1. **Installer les dépendances** (première fois seulement) :
   - Double-cliquez sur `install_requirements.bat`
   - Ou exécutez : `pip install pillow reportlab matplotlib`

2. **Lancer l'application** :
   - Double-cliquez sur `start.bat`
   - Ou exécutez : `python main.py`

### Test de l'installation
- Exécutez `python test_app.py` pour vérifier que tout fonctionne

## Fonctionnalités Principales

### 1. Tableau de Bord
- **Statistiques générales** : Vue d'ensemble des données
- **Alertes stock** : Produits en rupture ou stock faible
- **Indicateurs clés** : Nombre de clients, produits, commandes, CA

### 2. Gestion des Clients
- **Ajouter un client** : Bouton "Nouveau Client"
- **Modifier un client** : Sélectionner et cliquer "Modifier"
- **Rechercher** : Utiliser la barre de recherche
- **Supprimer** : Sélectionner et cliquer "Supprimer" (désactivation)

**Champs obligatoires** : Nom et Prénom

### 3. Gestion des Produits
- **Ajouter un produit** : Bouton "Nouveau Produit"
- **Modifier un produit** : Sélectionner et cliquer "Modifier"
- **Ajuster le stock** : Saisir quantité et cliquer "Ajouter" ou "Retirer"
- **Filtrer** : Cocher "Produits en rupture" pour voir les alertes
- **Rechercher** : Par nom, description ou code produit

**Champs obligatoires** : Nom et Prix unitaire

### 4. Gestion des Commandes
- **Nouvelle commande** : Bouton "Nouvelle Commande"
  1. Sélectionner un client
  2. Ajouter des produits avec quantités
  3. Ajuster la TVA si nécessaire
  4. Ajouter des notes (optionnel)
  5. Enregistrer

- **Voir les détails** : Double-clic ou bouton "Voir Détails"
- **Modifier une commande** : Seulement si pas encore livrée/annulée
- **Changer le statut** : En attente → Confirmée → Expédiée → Livrée
- **Filtrer par statut** : Menu déroulant en haut à gauche

### 5. Codes Couleur des Commandes
- **Jaune** : En attente
- **Vert** : Confirmée
- **Bleu** : Expédiée
- **Bleu clair** : Livrée
- **Rouge** : Annulée

## Utilisation Quotidienne

### Workflow Typique
1. **Ajouter des clients** (si nouveaux)
2. **Vérifier le stock** des produits
3. **Créer des commandes** pour les clients
4. **Suivre les commandes** en changeant leur statut
5. **Consulter les rapports** pour analyser les ventes

### Gestion du Stock
- Les **alertes de rupture** apparaissent automatiquement
- **Ajuster le stock** après réception de marchandises
- **Vérifier régulièrement** les produits en stock faible

### Suivi des Commandes
- **En attente** : Commande créée, en attente de confirmation
- **Confirmée** : Commande validée, prête pour préparation
- **Expédiée** : Commande envoyée au client
- **Livrée** : Commande reçue par le client
- **Annulée** : Commande annulée

## Conseils et Bonnes Pratiques

### Saisie des Données
- **Codes produits** : Utilisez un système cohérent (ex: CAT-001, CAT-002)
- **Informations clients** : Remplissez au maximum pour un meilleur suivi
- **Stock minimum** : Définissez des seuils réalistes pour éviter les ruptures

### Sauvegarde
- La base de données est dans le dossier `data/`
- **Sauvegardez régulièrement** le fichier `gestion_commerciale.db`
- Copiez tout le dossier `data/` pour une sauvegarde complète

### Performance
- L'application peut gérer **plusieurs milliers** de clients/produits/commandes
- Pour de gros volumes, pensez à **archiver** les anciennes données

## Résolution de Problèmes

### L'application ne démarre pas
1. Vérifiez que Python est installé : `python --version`
2. Installez les dépendances : `pip install pillow reportlab matplotlib`
3. Vérifiez les permissions du dossier

### Erreur de base de données
1. Vérifiez que le dossier `data/` existe
2. Supprimez le fichier `data/gestion_commerciale.db` pour réinitialiser
3. Relancez l'application

### Interface qui ne répond pas
1. Fermez l'application
2. Relancez avec `python main.py`
3. Vérifiez la console pour les erreurs

### Données manquantes
1. Utilisez `python test_app.py` pour vérifier l'état
2. Les données d'exemple sont créées automatiquement au premier lancement

## Structure des Fichiers

```
gui/
├── main.py                 # Point d'entrée principal
├── start.bat              # Script de démarrage Windows
├── test_app.py            # Script de test
├── requirements.txt       # Dépendances Python
├── data/                  # Base de données SQLite
│   └── gestion_commerciale.db
├── src/                   # Code source
│   ├── database/          # Gestion BDD
│   ├── models/            # Modèles de données
│   ├── gui/               # Interface graphique
│   └── utils/             # Utilitaires
└── exports/               # Rapports exportés (créé automatiquement)
```

## Support

Pour toute question ou problème :
1. Consultez ce guide
2. Vérifiez les messages d'erreur dans la console
3. Utilisez le script de test pour diagnostiquer les problèmes

---

**Version** : 1.0  
**Dernière mise à jour** : Décembre 2024
