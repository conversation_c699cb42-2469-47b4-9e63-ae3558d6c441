#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Modèle Facture
"""

from datetime import datetime, timedelta
from src.database.database import DatabaseManager

class LigneFacture:
    """Modèle pour une ligne de facture"""
    
    def __init__(self, id=None, facture_id=None, produit_id=None, 
                 quantite=0, prix_unitaire=0.0, total_ligne=0.0):
        self.id = id
        self.facture_id = facture_id
        self.produit_id = produit_id
        self.quantite = quantite
        self.prix_unitaire = prix_unitaire
        self.total_ligne = total_ligne or (quantite * prix_unitaire)

class Facture:
    """Modèle pour gérer les factures"""
    
    def __init__(self, id=None, numero_facture="", commande_id=None, client_id=None,
                 date_facture=None, date_echeance=None, statut="Non payée", 
                 total_ht=0.0, tva=20.0, total_ttc=0.0, notes=""):
        self.id = id
        self.numero_facture = numero_facture
        self.commande_id = commande_id
        self.client_id = client_id
        self.date_facture = date_facture or datetime.now()
        self.date_echeance = date_echeance or (datetime.now() + timedelta(days=30))
        self.statut = statut
        self.total_ht = total_ht
        self.tva = tva
        self.total_ttc = total_ttc
        self.notes = notes
        self.lignes = []
        self.db = DatabaseManager()
    
    def generer_numero_facture(self):
        """Génère un numéro de facture unique"""
        if not self.numero_facture:
            date_str = datetime.now().strftime("%Y%m%d")
            # Compter les factures du jour
            query = "SELECT COUNT(*) FROM factures WHERE date_facture >= date('now')"
            result = self.db.execute_query(query)
            count = result[0][0] + 1
            self.numero_facture = f"FACT-{date_str}-{count:04d}"
    
    def ajouter_ligne(self, produit_id, quantite, prix_unitaire):
        """Ajoute une ligne à la facture"""
        total_ligne = quantite * prix_unitaire
        ligne = LigneFacture(
            facture_id=self.id,
            produit_id=produit_id,
            quantite=quantite,
            prix_unitaire=prix_unitaire,
            total_ligne=total_ligne
        )
        self.lignes.append(ligne)
        self.calculer_totaux()
    
    def supprimer_ligne(self, index):
        """Supprime une ligne de la facture"""
        if 0 <= index < len(self.lignes):
            del self.lignes[index]
            self.calculer_totaux()
    
    def calculer_totaux(self):
        """Calcule les totaux de la facture"""
        self.total_ht = sum(ligne.total_ligne for ligne in self.lignes)
        self.total_ttc = self.total_ht * (1 + self.tva / 100)
    
    def save(self):
        """Sauvegarde la facture en base de données"""
        if not self.numero_facture:
            self.generer_numero_facture()
        
        self.calculer_totaux()
        
        if self.id is None:
            # Nouvelle facture
            query = """INSERT INTO factures (numero_facture, commande_id, client_id, 
                      date_facture, date_echeance, statut, total_ht, tva, total_ttc, notes) 
                      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"""
            params = (self.numero_facture, self.commande_id, self.client_id, 
                     self.date_facture, self.date_echeance, self.statut, 
                     self.total_ht, self.tva, self.total_ttc, self.notes)
            self.id = self.db.execute_query(query, params)
        else:
            # Mise à jour facture existante
            query = """UPDATE factures SET numero_facture=?, commande_id=?, client_id=?, 
                      date_facture=?, date_echeance=?, statut=?, total_ht=?, tva=?, 
                      total_ttc=?, notes=? WHERE id=?"""
            params = (self.numero_facture, self.commande_id, self.client_id, 
                     self.date_facture, self.date_echeance, self.statut, 
                     self.total_ht, self.tva, self.total_ttc, self.notes, self.id)
            self.db.execute_query(query, params)
        
        # Sauvegarder les lignes
        self.sauvegarder_lignes()
        return self.id
    
    def sauvegarder_lignes(self):
        """Sauvegarde les lignes de facture"""
        if self.id:
            # Supprimer les anciennes lignes
            query = "DELETE FROM lignes_facture WHERE facture_id=?"
            self.db.execute_query(query, (self.id,))
            
            # Insérer les nouvelles lignes
            if self.lignes:
                query = """INSERT INTO lignes_facture (facture_id, produit_id, quantite, 
                          prix_unitaire, total_ligne) VALUES (?, ?, ?, ?, ?)"""
                params_list = []
                for ligne in self.lignes:
                    params_list.append((self.id, ligne.produit_id, ligne.quantite,
                                      ligne.prix_unitaire, ligne.total_ligne))
                self.db.execute_many(query, params_list)
    
    def charger_lignes(self):
        """Charge les lignes de facture depuis la base"""
        if self.id:
            query = """SELECT lf.*, p.nom as produit_nom 
                      FROM lignes_facture lf 
                      JOIN produits p ON lf.produit_id = p.id 
                      WHERE lf.facture_id=?"""
            results = self.db.execute_query(query, (self.id,))
            
            self.lignes = []
            for row in results:
                ligne = LigneFacture(
                    id=row['id'],
                    facture_id=row['facture_id'],
                    produit_id=row['produit_id'],
                    quantite=row['quantite'],
                    prix_unitaire=row['prix_unitaire'],
                    total_ligne=row['total_ligne']
                )
                self.lignes.append(ligne)
    
    @classmethod
    def get_by_id(cls, facture_id):
        """Récupère une facture par son ID"""
        db = DatabaseManager()
        query = "SELECT * FROM factures WHERE id=?"
        result = db.execute_query(query, (facture_id,))
        if result:
            row = result[0]
            facture = cls(
                id=row['id'], numero_facture=row['numero_facture'],
                commande_id=row['commande_id'], client_id=row['client_id'],
                date_facture=row['date_facture'], date_echeance=row['date_echeance'],
                statut=row['statut'], total_ht=row['total_ht'],
                tva=row['tva'], total_ttc=row['total_ttc'],
                notes=row['notes']
            )
            facture.charger_lignes()
            return facture
        return None
    
    @classmethod
    def get_all(cls):
        """Récupère toutes les factures"""
        db = DatabaseManager()
        query = """SELECT f.*, cl.nom as client_nom, cl.prenom as client_prenom 
                  FROM factures f 
                  JOIN clients cl ON f.client_id = cl.id 
                  ORDER BY f.date_facture DESC"""
        results = db.execute_query(query)
        
        factures = []
        for row in results:
            facture = cls(
                id=row['id'], numero_facture=row['numero_facture'],
                commande_id=row['commande_id'], client_id=row['client_id'],
                date_facture=row['date_facture'], date_echeance=row['date_echeance'],
                statut=row['statut'], total_ht=row['total_ht'],
                tva=row['tva'], total_ttc=row['total_ttc'],
                notes=row['notes']
            )
            factures.append(facture)
        return factures
    
    @classmethod
    def get_by_client(cls, client_id):
        """Récupère les factures d'un client"""
        db = DatabaseManager()
        query = "SELECT * FROM factures WHERE client_id=? ORDER BY date_facture DESC"
        results = db.execute_query(query, (client_id,))
        
        factures = []
        for row in results:
            facture = cls(
                id=row['id'], numero_facture=row['numero_facture'],
                commande_id=row['commande_id'], client_id=row['client_id'],
                date_facture=row['date_facture'], date_echeance=row['date_echeance'],
                statut=row['statut'], total_ht=row['total_ht'],
                tva=row['tva'], total_ttc=row['total_ttc'],
                notes=row['notes']
            )
            factures.append(facture)
        return factures
    
    @classmethod
    def creer_depuis_commande(cls, commande):
        """Crée une facture à partir d'une commande"""
        facture = cls(
            commande_id=commande.id,
            client_id=commande.client_id,
            tva=commande.tva,
            notes=commande.notes
        )
        
        # Copier les lignes de la commande
        commande.charger_lignes()
        for ligne_commande in commande.lignes:
            facture.ajouter_ligne(
                ligne_commande.produit_id,
                ligne_commande.quantite,
                ligne_commande.prix_unitaire
            )
        
        return facture
    
    def est_en_retard(self):
        """Vérifie si la facture est en retard de paiement"""
        if self.statut == "Non payée" and self.date_echeance:
            if isinstance(self.date_echeance, str):
                try:
                    date_echeance = datetime.fromisoformat(self.date_echeance.replace('Z', '+00:00'))
                except:
                    return False
            else:
                date_echeance = self.date_echeance
            
            return datetime.now() > date_echeance
        return False
    
    def __str__(self):
        """Représentation textuelle de la facture"""
        return f"{self.numero_facture} - {self.total_ttc:.2f}€ ({self.statut})"
