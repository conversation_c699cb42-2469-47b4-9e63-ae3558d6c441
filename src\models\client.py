#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Modèle Client
"""

from datetime import datetime
from src.database.database import DatabaseManager

class Client:
    """Modèle pour gérer les clients"""
    
    def __init__(self, id=None, nom="", prenom="", entreprise="", email="", 
                 telephone="", adresse="", ville="", code_postal="", pays="France",
                 date_creation=None, actif=True):
        self.id = id
        self.nom = nom
        self.prenom = prenom
        self.entreprise = entreprise
        self.email = email
        self.telephone = telephone
        self.adresse = adresse
        self.ville = ville
        self.code_postal = code_postal
        self.pays = pays
        self.date_creation = date_creation or datetime.now()
        self.actif = actif
        self.db = DatabaseManager()
    
    def save(self):
        """Sauvegarde le client en base de données"""
        if self.id is None:
            # Nouveau client
            query = """INSERT INTO clients (nom, prenom, entreprise, email, telephone, 
                      adresse, ville, code_postal, pays, actif) 
                      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"""
            params = (self.nom, self.prenom, self.entreprise, self.email, self.telephone,
                     self.adresse, self.ville, self.code_postal, self.pays, self.actif)
            self.id = self.db.execute_query(query, params)
        else:
            # Mise à jour client existant
            query = """UPDATE clients SET nom=?, prenom=?, entreprise=?, email=?, 
                      telephone=?, adresse=?, ville=?, code_postal=?, pays=?, actif=? 
                      WHERE id=?"""
            params = (self.nom, self.prenom, self.entreprise, self.email, self.telephone,
                     self.adresse, self.ville, self.code_postal, self.pays, self.actif, self.id)
            self.db.execute_query(query, params)
        return self.id
    
    def delete(self):
        """Supprime le client (désactivation)"""
        if self.id:
            query = "UPDATE clients SET actif=0 WHERE id=?"
            self.db.execute_query(query, (self.id,))
            self.actif = False
    
    @classmethod
    def get_by_id(cls, client_id):
        """Récupère un client par son ID"""
        db = DatabaseManager()
        query = "SELECT * FROM clients WHERE id=? AND actif=1"
        result = db.execute_query(query, (client_id,))
        if result:
            row = result[0]
            return cls(
                id=row['id'], nom=row['nom'], prenom=row['prenom'],
                entreprise=row['entreprise'], email=row['email'],
                telephone=row['telephone'], adresse=row['adresse'],
                ville=row['ville'], code_postal=row['code_postal'],
                pays=row['pays'], date_creation=row['date_creation'],
                actif=row['actif']
            )
        return None
    
    @classmethod
    def get_all(cls, actifs_seulement=True):
        """Récupère tous les clients"""
        db = DatabaseManager()
        if actifs_seulement:
            query = "SELECT * FROM clients WHERE actif=1 ORDER BY nom, prenom"
        else:
            query = "SELECT * FROM clients ORDER BY nom, prenom"
        results = db.execute_query(query)
        
        clients = []
        for row in results:
            client = cls(
                id=row['id'], nom=row['nom'], prenom=row['prenom'],
                entreprise=row['entreprise'], email=row['email'],
                telephone=row['telephone'], adresse=row['adresse'],
                ville=row['ville'], code_postal=row['code_postal'],
                pays=row['pays'], date_creation=row['date_creation'],
                actif=row['actif']
            )
            clients.append(client)
        return clients
    
    @classmethod
    def search(cls, terme_recherche):
        """Recherche des clients par nom, prénom ou entreprise"""
        db = DatabaseManager()
        query = """SELECT * FROM clients 
                  WHERE (nom LIKE ? OR prenom LIKE ? OR entreprise LIKE ?) 
                  AND actif=1 
                  ORDER BY nom, prenom"""
        terme = f"%{terme_recherche}%"
        results = db.execute_query(query, (terme, terme, terme))
        
        clients = []
        for row in results:
            client = cls(
                id=row['id'], nom=row['nom'], prenom=row['prenom'],
                entreprise=row['entreprise'], email=row['email'],
                telephone=row['telephone'], adresse=row['adresse'],
                ville=row['ville'], code_postal=row['code_postal'],
                pays=row['pays'], date_creation=row['date_creation'],
                actif=row['actif']
            )
            clients.append(client)
        return clients
    
    def __str__(self):
        """Représentation textuelle du client"""
        if self.entreprise:
            return f"{self.nom} {self.prenom} ({self.entreprise})"
        else:
            return f"{self.nom} {self.prenom}"
    
    def get_nom_complet(self):
        """Retourne le nom complet du client"""
        return f"{self.prenom} {self.nom}"
    
    def get_adresse_complete(self):
        """Retourne l'adresse complète du client"""
        adresse_parts = []
        if self.adresse:
            adresse_parts.append(self.adresse)
        if self.code_postal and self.ville:
            adresse_parts.append(f"{self.code_postal} {self.ville}")
        if self.pays and self.pays != "France":
            adresse_parts.append(self.pays)
        return "\n".join(adresse_parts)
