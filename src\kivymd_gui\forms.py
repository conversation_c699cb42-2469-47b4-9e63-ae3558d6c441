#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Formulaires KivyMD pour l'application de gestion commerciale
"""

from kivymd.uix.dialog import MDDialog
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.textfield import MD<PERSON><PERSON>t<PERSON>ield
from kivymd.uix.button import MD<PERSON><PERSON><PERSON>utton, MDRaisedButton
from kivymd.uix.label import MDLabel
from kivymd.uix.selectioncontrol import MDSwitch
from kivymd.uix.menu import MDDropdownMenu
from kivymd.uix.list import OneLineListItem

from kivy.metrics import dp

class BaseForm:
    """Classe de base pour les formulaires"""
    
    def __init__(self, title, callback=None):
        self.title = title
        self.callback = callback
        self.dialog = None
        self.fields = {}
        self.validation_rules = {}
    
    def add_text_field(self, key, label, hint_text="", required=False, input_type="text"):
        """Ajoute un champ de texte"""
        field = MDTextField(
            hint_text=hint_text or label,
            helper_text=f"{label} {'(obligatoire)' if required else '(optionnel)'}",
            helper_text_mode="on_focus",
            size_hint_y=None,
            height=dp(56)
        )
        
        if input_type == "email":
            field.input_filter = "email"
        elif input_type == "number":
            field.input_filter = "float"
        elif input_type == "phone":
            field.input_filter = "int"
        
        self.fields[key] = field
        
        if required:
            self.validation_rules[key] = lambda x: x.strip() != ""
        
        return field
    
    def add_switch(self, key, label, default=True):
        """Ajoute un switch"""
        layout = MDBoxLayout(
            orientation="horizontal",
            size_hint_y=None,
            height=dp(48),
            spacing=dp(10)
        )
        
        label_widget = MDLabel(
            text=label,
            theme_text_color="Primary",
            valign="center",
            size_hint_x=0.7
        )
        
        switch = MDSwitch(
            active=default,
            size_hint_x=0.3
        )
        
        layout.add_widget(label_widget)
        layout.add_widget(switch)
        
        self.fields[key] = switch
        return layout
    
    def validate(self):
        """Valide le formulaire"""
        errors = []
        
        for key, rule in self.validation_rules.items():
            field = self.fields[key]
            
            if hasattr(field, 'text'):
                value = field.text
            elif hasattr(field, 'active'):
                value = field.active
            else:
                continue
            
            if not rule(value):
                errors.append(f"Le champ {key} est invalide")
        
        return errors
    
    def get_values(self):
        """Récupère les valeurs du formulaire"""
        values = {}
        
        for key, field in self.fields.items():
            if hasattr(field, 'text'):
                values[key] = field.text.strip()
            elif hasattr(field, 'active'):
                values[key] = field.active
        
        return values
    
    def show(self):
        """Affiche le formulaire"""
        if self.dialog:
            self.dialog.open()
    
    def close(self):
        """Ferme le formulaire"""
        if self.dialog:
            self.dialog.dismiss()

class ClientForm(BaseForm):
    """Formulaire pour créer/modifier un client"""
    
    def __init__(self, client=None, callback=None):
        super().__init__(
            "Nouveau Client" if client is None else "Modifier Client",
            callback
        )
        self.client = client
        self.build_form()
    
    def build_form(self):
        """Construit le formulaire client"""
        content = MDBoxLayout(
            orientation="vertical",
            spacing=dp(10),
            size_hint_y=None,
            height=dp(500)
        )
        
        # Champs du formulaire
        content.add_widget(self.add_text_field(
            "prenom", "Prénom", required=True
        ))
        
        content.add_widget(self.add_text_field(
            "nom", "Nom", required=True
        ))
        
        content.add_widget(self.add_text_field(
            "entreprise", "Entreprise"
        ))
        
        content.add_widget(self.add_text_field(
            "email", "Email", input_type="email"
        ))
        
        content.add_widget(self.add_text_field(
            "telephone", "Téléphone", input_type="phone"
        ))
        
        content.add_widget(self.add_text_field(
            "adresse", "Adresse"
        ))
        
        content.add_widget(self.add_text_field(
            "ville", "Ville"
        ))
        
        content.add_widget(self.add_text_field(
            "code_postal", "Code postal"
        ))
        
        content.add_widget(self.add_switch(
            "actif", "Client actif", default=True
        ))
        
        # Charger les données si modification
        if self.client:
            self.load_client_data()
        
        # Créer le dialog
        self.dialog = MDDialog(
            title=self.title,
            type="custom",
            content_cls=content,
            buttons=[
                MDFlatButton(
                    text="ANNULER",
                    on_release=lambda x: self.close()
                ),
                MDRaisedButton(
                    text="ENREGISTRER",
                    on_release=lambda x: self.save_client()
                )
            ]
        )
    
    def load_client_data(self):
        """Charge les données du client dans le formulaire"""
        if self.client:
            self.fields["prenom"].text = self.client.prenom or ""
            self.fields["nom"].text = self.client.nom or ""
            self.fields["entreprise"].text = self.client.entreprise or ""
            self.fields["email"].text = self.client.email or ""
            self.fields["telephone"].text = self.client.telephone or ""
            self.fields["adresse"].text = self.client.adresse or ""
            self.fields["ville"].text = self.client.ville or ""
            self.fields["code_postal"].text = self.client.code_postal or ""
            self.fields["actif"].active = self.client.actif
    
    def save_client(self):
        """Sauvegarde le client"""
        # Valider le formulaire
        errors = self.validate()
        if errors:
            from kivymd.uix.snackbar import Snackbar
            snackbar = Snackbar()
            snackbar.text = f"Erreurs: {', '.join(errors)}"
            snackbar.open()
            return
        
        try:
            from ..models.client import Client
            
            # Créer ou modifier le client
            if self.client is None:
                client = Client()
            else:
                client = self.client
            
            # Récupérer les valeurs
            values = self.get_values()
            
            # Remplir les données
            client.prenom = values["prenom"]
            client.nom = values["nom"]
            client.entreprise = values["entreprise"]
            client.email = values["email"]
            client.telephone = values["telephone"]
            client.adresse = values["adresse"]
            client.ville = values["ville"]
            client.code_postal = values["code_postal"]
            client.actif = values["actif"]
            
            # Sauvegarder
            client.save()
            
            # Fermer le formulaire
            self.close()
            
            # Appeler le callback
            if self.callback:
                self.callback()
            
            # Message de succès
            from kivymd.uix.snackbar import Snackbar
            snackbar = Snackbar()
            snackbar.text = "Client enregistré avec succès"
            snackbar.open()
            
        except Exception as e:
            from kivymd.uix.snackbar import Snackbar
            snackbar = Snackbar()
            snackbar.text = f"Erreur: {e}"
            snackbar.open()

class ProduitForm(BaseForm):
    """Formulaire pour créer/modifier un produit"""
    
    def __init__(self, produit=None, callback=None):
        super().__init__(
            "Nouveau Produit" if produit is None else "Modifier Produit",
            callback
        )
        self.produit = produit
        self.build_form()
    
    def build_form(self):
        """Construit le formulaire produit"""
        content = MDBoxLayout(
            orientation="vertical",
            spacing=dp(10),
            size_hint_y=None,
            height=dp(400)
        )
        
        # Champs du formulaire
        content.add_widget(self.add_text_field(
            "nom", "Nom du produit", required=True
        ))
        
        content.add_widget(self.add_text_field(
            "code_produit", "Code produit"
        ))
        
        content.add_widget(self.add_text_field(
            "description", "Description"
        ))
        
        content.add_widget(self.add_text_field(
            "prix_unitaire", "Prix unitaire (€)", required=True, input_type="number"
        ))
        
        content.add_widget(self.add_text_field(
            "stock_actuel", "Stock actuel", input_type="number"
        ))
        
        content.add_widget(self.add_text_field(
            "stock_minimum", "Stock minimum", input_type="number"
        ))
        
        content.add_widget(self.add_switch(
            "actif", "Produit actif", default=True
        ))
        
        # Validation personnalisée pour les prix
        self.validation_rules["prix_unitaire"] = lambda x: self.validate_price(x)
        
        # Charger les données si modification
        if self.produit:
            self.load_produit_data()
        
        # Créer le dialog
        self.dialog = MDDialog(
            title=self.title,
            type="custom",
            content_cls=content,
            buttons=[
                MDFlatButton(
                    text="ANNULER",
                    on_release=lambda x: self.close()
                ),
                MDRaisedButton(
                    text="ENREGISTRER",
                    on_release=lambda x: self.save_produit()
                )
            ]
        )
    
    def validate_price(self, value):
        """Valide un prix"""
        try:
            price = float(value.replace(',', '.'))
            return price >= 0
        except:
            return False
    
    def load_produit_data(self):
        """Charge les données du produit dans le formulaire"""
        if self.produit:
            self.fields["nom"].text = self.produit.nom or ""
            self.fields["code_produit"].text = self.produit.code_produit or ""
            self.fields["description"].text = self.produit.description or ""
            self.fields["prix_unitaire"].text = str(self.produit.prix_unitaire)
            self.fields["stock_actuel"].text = str(self.produit.stock_actuel)
            self.fields["stock_minimum"].text = str(self.produit.stock_minimum)
            self.fields["actif"].active = self.produit.actif
    
    def save_produit(self):
        """Sauvegarde le produit"""
        # Valider le formulaire
        errors = self.validate()
        if errors:
            from kivymd.uix.snackbar import Snackbar
            snackbar = Snackbar()
            snackbar.text = f"Erreurs: {', '.join(errors)}"
            snackbar.open()
            return
        
        try:
            from ..models.produit import Produit
            
            # Créer ou modifier le produit
            if self.produit is None:
                produit = Produit()
            else:
                produit = self.produit
            
            # Récupérer les valeurs
            values = self.get_values()
            
            # Remplir les données
            produit.nom = values["nom"]
            produit.code_produit = values["code_produit"]
            produit.description = values["description"]
            produit.prix_unitaire = float(values["prix_unitaire"].replace(',', '.'))
            produit.stock_actuel = int(values["stock_actuel"] or 0)
            produit.stock_minimum = int(values["stock_minimum"] or 0)
            produit.actif = values["actif"]
            
            # Sauvegarder
            produit.save()
            
            # Fermer le formulaire
            self.close()
            
            # Appeler le callback
            if self.callback:
                self.callback()
            
            # Message de succès
            from kivymd.uix.snackbar import Snackbar
            snackbar = Snackbar()
            snackbar.text = "Produit enregistré avec succès"
            snackbar.open()
            
        except Exception as e:
            from kivymd.uix.snackbar import Snackbar
            snackbar = Snackbar()
            snackbar.text = f"Erreur: {e}"
            snackbar.open()
