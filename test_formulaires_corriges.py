#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test des formulaires corrigés
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent / "src"))

from kivymd.app import MD<PERSON>pp
from kivymd.uix.screenmanager import MDScreenManager
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.label import MDLabel

from kivy.metrics import dp

# Test des imports
try:
    from formulaires_kivymd_fixed import ClientFormScreen, ProduitFormScreen
    print("✅ Formulaires corrigés importés avec succès")
    FORMULAIRES_OK = True
except ImportError as e:
    print(f"❌ Erreur d'import: {e}")
    FORMULAIRES_OK = False

class TestScreen(MDScreen):
    """Écran de test"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = "test"
        self.build_screen()
    
    def build_screen(self):
        """Construit l'écran de test"""
        layout = MDBoxLayout(orientation="vertical", spacing=dp(20), padding=dp(20))
        
        title = MDLabel(
            text="Test des Formulaires Corrigés",
            font_style="H4",
            theme_text_color="Primary",
            halign="center",
            size_hint_y=None,
            height=dp(60)
        )
        layout.add_widget(title)
        
        if FORMULAIRES_OK:
            status = MDLabel(
                text="✅ Formulaires disponibles",
                theme_text_color="Primary",
                halign="center",
                size_hint_y=None,
                height=dp(40)
            )
            layout.add_widget(status)
            
            # Boutons de test
            client_btn = MDRaisedButton(
                text="Tester Formulaire Client",
                size_hint_y=None,
                height=dp(50),
                on_release=self.test_client_form
            )
            layout.add_widget(client_btn)
            
            produit_btn = MDRaisedButton(
                text="Tester Formulaire Produit",
                size_hint_y=None,
                height=dp(50),
                on_release=self.test_produit_form
            )
            layout.add_widget(produit_btn)
        else:
            error = MDLabel(
                text="❌ Formulaires non disponibles",
                theme_text_color="Error",
                halign="center"
            )
            layout.add_widget(error)
        
        self.add_widget(layout)
    
    def test_client_form(self, *args):
        """Test du formulaire client"""
        print("🧪 Test du formulaire client...")
        try:
            app = MDApp.get_running_app()
            
            # Créer le formulaire
            client_form = ClientFormScreen(callback=self.on_form_closed)
            
            # Ajouter au screen manager
            if app.screen_manager.has_screen("client_form"):
                app.screen_manager.remove_widget(app.screen_manager.get_screen("client_form"))
            
            app.screen_manager.add_widget(client_form)
            app.screen_manager.current = "client_form"
            
            print("✅ Formulaire client ouvert avec succès")
            
        except Exception as e:
            print(f"❌ Erreur: {e}")
            import traceback
            traceback.print_exc()
    
    def test_produit_form(self, *args):
        """Test du formulaire produit"""
        print("🧪 Test du formulaire produit...")
        try:
            app = MDApp.get_running_app()
            
            # Créer le formulaire
            produit_form = ProduitFormScreen(callback=self.on_form_closed)
            
            # Ajouter au screen manager
            if app.screen_manager.has_screen("produit_form"):
                app.screen_manager.remove_widget(app.screen_manager.get_screen("produit_form"))
            
            app.screen_manager.add_widget(produit_form)
            app.screen_manager.current = "produit_form"
            
            print("✅ Formulaire produit ouvert avec succès")
            
        except Exception as e:
            print(f"❌ Erreur: {e}")
            import traceback
            traceback.print_exc()
    
    def on_form_closed(self):
        """Callback quand un formulaire est fermé"""
        print("🔙 Retour au test")
        app = MDApp.get_running_app()
        app.screen_manager.current = "test"

class TestApp(MDApp):
    """Application de test"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = "Test Formulaires Corrigés"
        self.theme_cls.primary_palette = "Blue"
        self.theme_cls.theme_style = "Light"
    
    def build(self):
        """Construit l'application"""
        print("🚀 Démarrage du test...")
        
        # Gestionnaire d'écrans
        self.screen_manager = MDScreenManager()
        
        # Ajouter l'écran de test
        test_screen = TestScreen()
        self.screen_manager.add_widget(test_screen)
        
        return self.screen_manager

def main():
    """Point d'entrée"""
    try:
        print("=" * 50)
        print("  TEST DES FORMULAIRES CORRIGÉS")
        print("=" * 50)
        print()
        
        app = TestApp()
        app.run()
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
