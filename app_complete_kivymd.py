#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Application de Gestion Commerciale - Version KivyMD Complète
Avec formulaires intégrés
"""

import sys
import os
from pathlib import Path

# Ajouter le répertoire src au path
sys.path.append(str(Path(__file__).parent / "src"))

from kivymd.app import MDApp
from kivymd.uix.screenmanager import MDScreenManager
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.toolbar import MDTopAppBar
from kivymd.uix.bottomnavigation import MDBottomNavigation, MDBottomNavigationItem
from kivymd.uix.label import MDLabel
from kivymd.uix.card import MDCard
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.list import MDList, OneLineListItem, TwoLineListItem, ThreeLineListItem
from kivymd.uix.button import MDRaisedButton, MDI<PERSON><PERSON>utton, MDFlatButton
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.dialog import MDDialog

from kivy.metrics import dp

# Imports des modèles
from src.database.database import DatabaseManager
from src.models.client import Client
from src.models.produit import Produit
from src.models.commande import Commande

# Imports des formulaires
try:
    from formulaires_kivymd import ClientFormScreen, ProduitFormScreen
    from formulaire_commande_kivymd import CommandeFormScreen
    FORMULAIRES_AVAILABLE = True
    print("✅ Formulaires corrigés importés avec succès")
except ImportError as e:
    print(f"⚠ Erreur d'import des formulaires: {e}")
    FORMULAIRES_AVAILABLE = False

class DashboardScreen(MDScreen):
    """Écran du tableau de bord"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = "dashboard"
        self.build_screen()
    
    def build_screen(self):
        """Construit l'écran du tableau de bord"""
        scroll = MDScrollView()
        layout = MDBoxLayout(orientation="vertical", spacing=dp(10), padding=dp(10))

        # Titre et bouton de rafraîchissement
        header_layout = MDBoxLayout(orientation="horizontal", size_hint_y=None, height=dp(60))

        title = MDLabel(
            text="Tableau de Bord",
            theme_text_color="Primary",
            font_style="H4",
            halign="center"
        )
        header_layout.add_widget(title)

        refresh_btn = MDIconButton(
            icon="refresh",
            size_hint=(None, None),
            size=(dp(40), dp(40)),
            on_release=self.refresh_dashboard,
            theme_icon_color="Primary"
        )
        header_layout.add_widget(refresh_btn)

        layout.add_widget(header_layout)

        # Alertes de stock (déplacé vers le haut)
        alerts_card = self.create_alerts_section()
        layout.add_widget(alerts_card)

        # Espace flexible pour pousser le contenu vers le bas
        spacer = MDLabel(text="", size_hint_y=1)
        layout.add_widget(spacer)

        # Section des boutons d'actions rapides (ajoutée)
        actions_section = self.create_actions_section()
        layout.add_widget(actions_section)

        # Grille pour les statistiques (déplacée vers le bas)
        stats_grid = MDGridLayout(cols=2, spacing=dp(10), size_hint_y=None, height=dp(200))

        try:
            db = DatabaseManager()

            # Statistiques
            stats = [
                ("Clients", "SELECT COUNT(*) FROM clients WHERE actif=1"),
                ("Produits", "SELECT COUNT(*) FROM produits WHERE actif=1"),
                ("Commandes", "SELECT COUNT(*) FROM commandes WHERE statut='En attente'"),
                ("CA Mois", "SELECT COALESCE(SUM(total_ttc), 0) FROM commandes WHERE date_commande >= date('now', 'start of month')")
            ]

            for title_stat, query in stats:
                result = db.execute_query(query)
                value = result[0][0] if result else 0

                if title_stat == "CA Mois":
                    value_text = f"{value:.2f}€"
                else:
                    value_text = str(value)

                card = self.create_stat_card(title_stat, value_text)
                stats_grid.add_widget(card)

        except Exception as e:
            error_label = MDLabel(text=f"Erreur: {e}", theme_text_color="Error")
            stats_grid.add_widget(error_label)

        layout.add_widget(stats_grid)

        scroll.add_widget(layout)
        self.add_widget(scroll)
    
    def create_stat_card(self, title, value):
        """Crée une carte de statistique cliquable"""
        card = MDCard(
            size_hint_y=None,
            height=dp(80),
            elevation=2,
            padding=dp(10),
            md_bg_color=(1, 1, 1, 1),
            on_release=lambda x: self.on_stat_card_click(title)
        )
        
        layout = MDBoxLayout(orientation="vertical")
        
        title_label = MDLabel(
            text=title,
            theme_text_color="Primary",
            font_style="Subtitle1",
            size_hint_y=None,
            height=dp(30),
            halign="center"
        )
        layout.add_widget(title_label)
        
        value_label = MDLabel(
            text=value,
            theme_text_color="Primary",
            font_style="H6",
            bold=True,
            halign="center"
        )
        layout.add_widget(value_label)
        
        card.add_widget(layout)
        return card
    
    def create_alerts_section(self):
        """Crée la section des alertes"""
        card = MDCard(
            size_hint_y=None,
            height=dp(200),
            elevation=2,
            padding=dp(10),
            md_bg_color=(1, 1, 1, 1)
        )
        
        layout = MDBoxLayout(orientation="vertical")
        
        title = MDLabel(
            text="Alertes Stock",
            theme_text_color="Primary",
            font_style="H6",
            size_hint_y=None,
            height=dp(30),
            halign="center"
        )
        layout.add_widget(title)
        
        alerts_list = MDList()
        
        try:
            db = DatabaseManager()
            query = """SELECT nom, stock_actuel, stock_minimum 
                      FROM produits 
                      WHERE stock_actuel <= stock_minimum AND actif=1 
                      ORDER BY nom LIMIT 5"""
            results = db.execute_query(query)
            
            if results:
                for row in results:
                    item = TwoLineListItem(
                        text=row[0],
                        secondary_text=f"Stock: {row[1]} (Min: {row[2]})",
                        on_release=lambda x, produit_nom=row[0]: self.on_alert_click(produit_nom)
                    )
                    alerts_list.add_widget(item)
            else:
                item = OneLineListItem(
                    text="Aucune alerte de stock",
                    on_release=lambda x: self.show_message("Aucune alerte actuellement")
                )
                alerts_list.add_widget(item)
                
        except Exception as e:
            item = OneLineListItem(text=f"Erreur: {e}")
            alerts_list.add_widget(item)
        
        layout.add_widget(alerts_list)
        card.add_widget(layout)
        
        return card

    def create_actions_section(self):
        """Crée la section des actions rapides"""
        # Carte principale pour les actions
        actions_card = MDCard(
            size_hint_y=None,
            height=dp(180),
            elevation=2,
            padding=dp(15),
            md_bg_color=(0.98, 0.98, 1, 1)
        )

        actions_layout = MDBoxLayout(orientation="vertical", spacing=dp(10))

        # Titre de la section
        actions_title = MDLabel(
            text="Actions Rapides",
            theme_text_color="Primary",
            font_style="H6",
            size_hint_y=None,
            height=dp(30),
            halign="center",
            bold=True
        )
        actions_layout.add_widget(actions_title)

        # Grille de boutons d'actions
        buttons_grid = MDGridLayout(cols=3, spacing=dp(10), size_hint_y=None, height=dp(120))

        # Boutons d'actions avec icônes
        actions = [
            ("Nouveau\nClient", "account-plus", self.open_client_form),
            ("Nouveau\nProduit", "package-variant-plus", self.open_produit_form),
            ("Nouvelle\nCommande", "cart-plus", self.open_commande_form)
        ]

        for text, icon, callback in actions:
            # Conteneur pour chaque bouton
            button_container = MDBoxLayout(orientation="vertical", spacing=dp(5))

            # Bouton icône
            icon_button = MDIconButton(
                icon=icon,
                size_hint=(None, None),
                size=(dp(50), dp(50)),
                theme_icon_color="Primary",
                on_release=callback
            )

            # Label du bouton
            button_label = MDLabel(
                text=text,
                theme_text_color="Primary",
                font_style="Caption",
                halign="center",
                size_hint_y=None,
                height=dp(30)
            )

            button_container.add_widget(icon_button)
            button_container.add_widget(button_label)
            buttons_grid.add_widget(button_container)

        actions_layout.add_widget(buttons_grid)
        actions_card.add_widget(actions_layout)

        return actions_card

    def refresh_dashboard(self, *args):
        """Rafraîchit le tableau de bord"""
        print("Bouton 'Rafraîchir' cliqué !")

        # Afficher un message de rafraîchissement
        self.show_message("Rafraîchissement en cours...")

        try:
            # Reconstruire l'écran
            self.clear_widgets()
            self.build_screen()
            print("Tableau de bord rafraîchi !")

            # Confirmer le rafraîchissement
            self.show_dialog("Information", "Tableau de bord actualisé avec succès !")

        except Exception as e:
            print(f"Erreur lors du rafraîchissement: {e}")
            self.show_dialog("Erreur", f"Erreur lors du rafraîchissement: {e}")

    def on_stat_card_click(self, title):
        """Gère le clic sur une carte de statistique"""
        print(f"Carte '{title}' cliquée !")

        # Navigation vers l'écran correspondant
        app = MDApp.get_running_app()

        if title == "Clients":
            # Proposer les options : voir la liste ou créer nouveau
            self.show_clients_options()
        elif title == "Produits":
            # Proposer les options : voir la liste ou créer nouveau
            self.show_produits_options()
        elif title == "Commandes":
            # Proposer les options : voir la liste ou créer nouveau
            self.show_commandes_options()
        elif title == "CA Mois":
            # Afficher des détails sur le CA
            self.show_ca_details()

        # Afficher un message de confirmation
        self.show_message(f"Options pour {title}")

    def show_clients_options(self):
        """Affiche les options pour les clients"""
        dialog = MDDialog(
            title="Gestion des Clients",
            text="Que souhaitez-vous faire ?",
            buttons=[
                MDFlatButton(
                    text="Voir la liste",
                    on_release=lambda x: self.navigate_to_clients(dialog)
                ),
                MDRaisedButton(
                    text="Nouveau client",
                    on_release=lambda x: self.open_client_form_from_dialog(dialog)
                )
            ]
        )
        dialog.open()

    def show_produits_options(self):
        """Affiche les options pour les produits"""
        dialog = MDDialog(
            title="Gestion des Produits",
            text="Que souhaitez-vous faire ?",
            buttons=[
                MDFlatButton(
                    text="Voir la liste",
                    on_release=lambda x: self.navigate_to_produits(dialog)
                ),
                MDRaisedButton(
                    text="Nouveau produit",
                    on_release=lambda x: self.open_produit_form_from_dialog(dialog)
                )
            ]
        )
        dialog.open()

    def show_commandes_options(self):
        """Affiche les options pour les commandes"""
        dialog = MDDialog(
            title="Gestion des Commandes",
            text="Que souhaitez-vous faire ?",
            buttons=[
                MDFlatButton(
                    text="Voir la liste",
                    on_release=lambda x: self.navigate_to_commandes(dialog)
                ),
                MDRaisedButton(
                    text="Nouvelle commande",
                    on_release=lambda x: self.open_commande_form_from_dialog(dialog)
                )
            ]
        )
        dialog.open()

    def navigate_to_clients(self, dialog):
        """Navigue vers l'écran clients"""
        dialog.dismiss()
        app = MDApp.get_running_app()
        app.screen_manager.current = "clients"

    def navigate_to_produits(self, dialog):
        """Navigue vers l'écran produits"""
        dialog.dismiss()
        app = MDApp.get_running_app()
        app.screen_manager.current = "produits"

    def navigate_to_commandes(self, dialog):
        """Navigue vers l'écran commandes"""
        dialog.dismiss()
        app = MDApp.get_running_app()
        app.screen_manager.current = "commandes"

    def open_client_form_from_dialog(self, dialog):
        """Ouvre le formulaire client depuis le dialog"""
        dialog.dismiss()
        self.open_client_form()

    def open_produit_form_from_dialog(self, dialog):
        """Ouvre le formulaire produit depuis le dialog"""
        dialog.dismiss()
        self.open_produit_form()

    def open_commande_form_from_dialog(self, dialog):
        """Ouvre le formulaire commande depuis le dialog"""
        dialog.dismiss()
        self.open_commande_form()

    def open_client_form(self, *args):
        """Ouvre le formulaire de nouveau client"""
        print("🎯 Ouverture du formulaire client...")

        try:
            if not FORMULAIRES_AVAILABLE:
                self.show_dialog("Erreur", "Les formulaires ne sont pas disponibles")
                return

            app = MDApp.get_running_app()

            # Créer le formulaire
            client_form = ClientFormScreen(callback=self.on_client_form_closed)

            # Ajouter à l'écran manager s'il n'existe pas déjà
            if app.screen_manager.has_screen("client_form"):
                app.screen_manager.remove_widget(app.screen_manager.get_screen("client_form"))

            app.screen_manager.add_widget(client_form)

            # Naviguer vers le formulaire
            app.screen_manager.current = "client_form"
            print("✅ Formulaire client ouvert depuis le dashboard")

        except Exception as e:
            print(f"❌ Erreur lors de l'ouverture du formulaire client: {e}")
            self.show_dialog("Erreur", f"Impossible d'ouvrir le formulaire client: {e}")

    def open_produit_form(self, *args):
        """Ouvre le formulaire de nouveau produit"""
        print("🎯 Ouverture du formulaire produit...")

        try:
            if not FORMULAIRES_AVAILABLE:
                self.show_dialog("Erreur", "Les formulaires ne sont pas disponibles")
                return

            app = MDApp.get_running_app()

            # Créer le formulaire
            produit_form = ProduitFormScreen(callback=self.on_produit_form_closed)

            # Ajouter à l'écran manager
            if app.screen_manager.has_screen("produit_form"):
                app.screen_manager.remove_widget(app.screen_manager.get_screen("produit_form"))

            app.screen_manager.add_widget(produit_form)

            # Naviguer vers le formulaire
            app.screen_manager.current = "produit_form"
            print("✅ Formulaire produit ouvert depuis le dashboard")

        except Exception as e:
            print(f"❌ Erreur lors de l'ouverture du formulaire produit: {e}")
            self.show_dialog("Erreur", f"Impossible d'ouvrir le formulaire produit: {e}")

    def open_commande_form(self, *args):
        """Ouvre le formulaire de nouvelle commande"""
        print("🎯 Ouverture du formulaire commande...")

        try:
            if not FORMULAIRES_AVAILABLE:
                self.show_dialog("Erreur", "Les formulaires ne sont pas disponibles")
                return

            app = MDApp.get_running_app()

            # Créer le formulaire
            commande_form = CommandeFormScreen(callback=self.on_commande_form_closed)

            # Ajouter à l'écran manager
            if app.screen_manager.has_screen("commande_form"):
                app.screen_manager.remove_widget(app.screen_manager.get_screen("commande_form"))

            app.screen_manager.add_widget(commande_form)

            # Naviguer vers le formulaire
            app.screen_manager.current = "commande_form"
            print("✅ Formulaire commande ouvert depuis le dashboard")

        except Exception as e:
            print(f"❌ Erreur lors de l'ouverture du formulaire commande: {e}")
            self.show_dialog("Erreur", f"Impossible d'ouvrir le formulaire commande: {e}")

    def update_bottom_navigation(self, screen_name):
        """Met à jour la navigation inférieure"""
        # Cette méthode sera appelée pour synchroniser la navigation
        pass

    def show_ca_details(self):
        """Affiche les détails du chiffre d'affaires"""
        try:
            db = DatabaseManager()

            # Requêtes pour les détails du CA
            queries = {
                "CA Total": "SELECT COALESCE(SUM(total_ttc), 0) FROM commandes",
                "CA Mois": "SELECT COALESCE(SUM(total_ttc), 0) FROM commandes WHERE date_commande >= date('now', 'start of month')",
                "CA Semaine": "SELECT COALESCE(SUM(total_ttc), 0) FROM commandes WHERE date_commande >= date('now', '-7 days')",
                "Commandes Mois": "SELECT COUNT(*) FROM commandes WHERE date_commande >= date('now', 'start of month')"
            }

            details = []
            for label, query in queries.items():
                result = db.execute_query(query)
                value = result[0][0] if result else 0
                if "CA" in label:
                    details.append(f"{label}: {value:.2f}€")
                else:
                    details.append(f"{label}: {value}")

            message = "\n".join(details)
            self.show_dialog("Détails Chiffre d'Affaires", message)

        except Exception as e:
            self.show_dialog("Erreur", f"Impossible de charger les détails: {e}")

    def show_message(self, message):
        """Affiche un message simple"""
        print(f"Message: {message}")

    def show_dialog(self, title, text):
        """Affiche un dialog d'information"""
        dialog = MDDialog(
            title=title,
            text=text,
            buttons=[
                MDFlatButton(
                    text="OK",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()

    def on_alert_click(self, produit_nom):
        """Gère le clic sur une alerte de stock"""
        print(f"Alerte cliquée pour le produit: {produit_nom}")

        try:
            # Rechercher le produit par nom
            produits = Produit.get_all()
            produit = None
            for p in produits:
                if p.nom == produit_nom:
                    produit = p
                    break

            if produit:
                # Afficher les détails du produit
                details = [
                    f"Produit: {produit.nom}",
                    f"Stock actuel: {produit.stock_actuel}",
                    f"Stock minimum: {produit.stock_minimum}",
                    f"Prix: {produit.prix_unitaire:.2f}€",
                    f"Statut: {'Actif' if produit.actif else 'Inactif'}"
                ]

                if produit.code_produit:
                    details.insert(1, f"Code: {produit.code_produit}")

                message = "\n".join(details)
                self.show_dialog(f"Détails - {produit_nom}", message)

                # Proposer de naviguer vers les produits
                self.show_message(f"Cliquez sur 'Produits' pour modifier {produit_nom}")
            else:
                self.show_dialog("Erreur", f"Produit '{produit_nom}' non trouvé")

        except Exception as e:
            self.show_dialog("Erreur", f"Impossible de charger les détails: {e}")

    def on_client_form_closed(self):
        """Appelé quand le formulaire client est fermé"""
        print("Formulaire client fermé, retour au dashboard")
        # Rafraîchir le dashboard
        self.refresh_dashboard()
        # Retourner au dashboard
        app = MDApp.get_running_app()
        app.screen_manager.current = "dashboard"

    def on_produit_form_closed(self):
        """Appelé quand le formulaire produit est fermé"""
        print("Formulaire produit fermé, retour au dashboard")
        # Rafraîchir le dashboard
        self.refresh_dashboard()
        # Retourner au dashboard
        app = MDApp.get_running_app()
        app.screen_manager.current = "dashboard"

    def on_commande_form_closed(self):
        """Appelé quand le formulaire commande est fermé"""
        print("Formulaire commande fermé, retour au dashboard")
        # Rafraîchir le dashboard
        self.refresh_dashboard()
        # Retourner au dashboard
        app = MDApp.get_running_app()
        app.screen_manager.current = "dashboard"

class ClientsScreen(MDScreen):
    """Écran de gestion des clients avec formulaires"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = "clients"
        self.build_screen()
    
    def build_screen(self):
        """Construit l'écran des clients"""
        layout = MDBoxLayout(orientation="vertical", spacing=dp(10), padding=dp(10))
        
        # Titre
        title = MDLabel(
            text="Gestion des Clients",
            theme_text_color="Primary",
            font_style="H5",
            size_hint_y=None,
            height=dp(60),
            halign="center"
        )
        layout.add_widget(title)
        
        # Bouton d'ajout
        add_btn = MDRaisedButton(
            text="Nouveau Client",
            size_hint=(None, None),
            size=(dp(150), dp(40)),
            pos_hint={"center_x": 0.5},
            on_release=self.add_client
        )
        layout.add_widget(add_btn)
        
        # Liste des clients
        scroll = MDScrollView()
        self.clients_list = MDList()
        self.load_clients()
        
        scroll.add_widget(self.clients_list)
        layout.add_widget(scroll)
        
        self.add_widget(layout)
    
    def load_clients(self):
        """Charge la liste des clients"""
        self.clients_list.clear_widgets()
        
        try:
            clients = Client.get_all()
            for client in clients:
                item = ThreeLineListItem(
                    text=f"{client.prenom} {client.nom}",
                    secondary_text=client.entreprise or "Particulier",
                    tertiary_text=f"{client.email or ''} • {client.telephone or ''}",
                    on_release=lambda x, c=client: self.edit_client(c)
                )
                self.clients_list.add_widget(item)
        except Exception as e:
            item = OneLineListItem(text=f"Erreur: {e}")
            self.clients_list.add_widget(item)
    
    def add_client(self, *args):
        """Ajoute un nouveau client"""
        print("🎯 Ouverture formulaire nouveau client...")
        try:
            if not FORMULAIRES_AVAILABLE:
                self.show_error("Les formulaires ne sont pas disponibles")
                return

            app = MDApp.get_running_app()

            # Supprimer l'écran existant s'il y en a un
            if app.screen_manager.has_screen("client_form"):
                app.screen_manager.remove_widget(app.screen_manager.get_screen("client_form"))

            client_form = ClientFormScreen(callback=self.load_clients)
            app.screen_manager.add_widget(client_form)
            app.screen_manager.current = "client_form"
            print("✅ Formulaire client ouvert")
        except Exception as e:
            print(f"❌ Erreur: {e}")
            self.show_error(f"Erreur: {e}")

    def edit_client(self, client):
        """Modifie un client"""
        print(f"🎯 Modification client: {client.prenom} {client.nom}")
        try:
            if not FORMULAIRES_AVAILABLE:
                self.show_error("Les formulaires ne sont pas disponibles")
                return

            app = MDApp.get_running_app()

            # Supprimer l'écran existant s'il y en a un
            if app.screen_manager.has_screen("client_form"):
                app.screen_manager.remove_widget(app.screen_manager.get_screen("client_form"))

            client_form = ClientFormScreen(client=client, callback=self.load_clients)
            app.screen_manager.add_widget(client_form)
            app.screen_manager.current = "client_form"
            print("✅ Formulaire modification client ouvert")
        except Exception as e:
            print(f"❌ Erreur: {e}")
            self.show_error(f"Erreur: {e}")

    def show_error(self, message):
        """Affiche un message d'erreur"""
        from kivymd.uix.dialog import MDDialog
        from kivymd.uix.button import MDFlatButton

        dialog = MDDialog(
            title="Erreur",
            text=message,
            buttons=[
                MDFlatButton(
                    text="OK",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()

class ProduitsScreen(MDScreen):
    """Écran de gestion des produits avec formulaires"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = "produits"
        self.build_screen()
    
    def build_screen(self):
        """Construit l'écran des produits"""
        layout = MDBoxLayout(orientation="vertical", spacing=dp(10), padding=dp(10))
        
        # Titre
        title = MDLabel(
            text="Gestion des Produits",
            theme_text_color="Primary",
            font_style="H5",
            size_hint_y=None,
            height=dp(60),
            halign="center"
        )
        layout.add_widget(title)
        
        # Bouton d'ajout
        add_btn = MDRaisedButton(
            text="Nouveau Produit",
            size_hint=(None, None),
            size=(dp(150), dp(40)),
            pos_hint={"center_x": 0.5},
            on_release=self.add_produit
        )
        layout.add_widget(add_btn)
        
        # Liste des produits
        scroll = MDScrollView()
        self.produits_list = MDList()
        self.load_produits()
        
        scroll.add_widget(self.produits_list)
        layout.add_widget(scroll)
        
        self.add_widget(layout)
    
    def load_produits(self):
        """Charge la liste des produits"""
        self.produits_list.clear_widgets()
        
        try:
            produits = Produit.get_all()
            for produit in produits:
                status = "RUPTURE" if produit.est_en_rupture() else "OK"
                item = ThreeLineListItem(
                    text=produit.nom,
                    secondary_text=f"{produit.prix_unitaire:.2f}€ • {produit.code_produit or 'Sans code'}",
                    tertiary_text=f"Stock: {produit.stock_actuel} • {status}",
                    on_release=lambda x, p=produit: self.edit_produit(p)
                )
                self.produits_list.add_widget(item)
        except Exception as e:
            item = OneLineListItem(text=f"Erreur: {e}")
            self.produits_list.add_widget(item)
    
    def add_produit(self, *args):
        """Ajoute un nouveau produit"""
        print("🎯 Ouverture formulaire nouveau produit...")
        try:
            if not FORMULAIRES_AVAILABLE:
                self.show_error("Les formulaires ne sont pas disponibles")
                return

            app = MDApp.get_running_app()

            # Supprimer l'écran existant s'il y en a un
            if app.screen_manager.has_screen("produit_form"):
                app.screen_manager.remove_widget(app.screen_manager.get_screen("produit_form"))

            produit_form = ProduitFormScreen(callback=self.load_produits)
            app.screen_manager.add_widget(produit_form)
            app.screen_manager.current = "produit_form"
            print("✅ Formulaire produit ouvert")
        except Exception as e:
            print(f"❌ Erreur: {e}")
            self.show_error(f"Erreur: {e}")

    def edit_produit(self, produit):
        """Modifie un produit"""
        print(f"🎯 Modification produit: {produit.nom}")
        try:
            if not FORMULAIRES_AVAILABLE:
                self.show_error("Les formulaires ne sont pas disponibles")
                return

            app = MDApp.get_running_app()

            # Supprimer l'écran existant s'il y en a un
            if app.screen_manager.has_screen("produit_form"):
                app.screen_manager.remove_widget(app.screen_manager.get_screen("produit_form"))

            produit_form = ProduitFormScreen(produit=produit, callback=self.load_produits)
            app.screen_manager.add_widget(produit_form)
            app.screen_manager.current = "produit_form"
            print("✅ Formulaire modification produit ouvert")
        except Exception as e:
            print(f"❌ Erreur: {e}")
            self.show_error(f"Erreur: {e}")

    def show_error(self, message):
        """Affiche un message d'erreur"""
        from kivymd.uix.dialog import MDDialog
        from kivymd.uix.button import MDFlatButton

        dialog = MDDialog(
            title="Erreur",
            text=message,
            buttons=[
                MDFlatButton(
                    text="OK",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()

class CommandesScreen(MDScreen):
    """Écran de gestion des commandes avec formulaires"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = "commandes"
        self.build_screen()

    def build_screen(self):
        """Construit l'écran des commandes"""
        layout = MDBoxLayout(orientation="vertical", spacing=dp(10), padding=dp(10))

        # Titre
        title = MDLabel(
            text="Gestion des Commandes",
            theme_text_color="Primary",
            font_style="H5",
            size_hint_y=None,
            height=dp(60),
            halign="center"
        )
        layout.add_widget(title)

        # Bouton d'ajout
        add_btn = MDRaisedButton(
            text="Nouvelle Commande",
            size_hint=(None, None),
            size=(dp(150), dp(40)),
            pos_hint={"center_x": 0.5},
            on_release=self.add_commande
        )
        layout.add_widget(add_btn)

        # Liste des commandes
        scroll = MDScrollView()
        self.commandes_list = MDList()
        self.load_commandes()

        scroll.add_widget(self.commandes_list)
        layout.add_widget(scroll)

        self.add_widget(layout)

    def load_commandes(self):
        """Charge la liste des commandes"""
        self.commandes_list.clear_widgets()

        try:
            commandes = Commande.get_all()
            for commande in commandes:
                client = Client.get_by_id(commande.client_id)
                client_nom = str(client) if client else "Client inconnu"

                item = ThreeLineListItem(
                    text=commande.numero_commande,
                    secondary_text=client_nom,
                    tertiary_text=f"{commande.statut} • {commande.total_ttc:.2f}€",
                    on_release=lambda x, c=commande: self.edit_commande(c)
                )
                self.commandes_list.add_widget(item)
        except Exception as e:
            item = OneLineListItem(text=f"Erreur: {e}")
            self.commandes_list.add_widget(item)

    def add_commande(self, *args):
        """Ajoute une nouvelle commande"""
        print("🎯 Ouverture formulaire nouvelle commande...")
        try:
            if not FORMULAIRES_AVAILABLE:
                self.show_error("Les formulaires ne sont pas disponibles")
                return

            app = MDApp.get_running_app()

            # Supprimer l'écran existant s'il y en a un
            if app.screen_manager.has_screen("commande_form"):
                app.screen_manager.remove_widget(app.screen_manager.get_screen("commande_form"))

            commande_form = CommandeFormScreen(callback=self.load_commandes)
            app.screen_manager.add_widget(commande_form)
            app.screen_manager.current = "commande_form"
            print("✅ Formulaire commande ouvert")
        except Exception as e:
            print(f"❌ Erreur: {e}")
            self.show_error(f"Erreur: {e}")

    def edit_commande(self, commande):
        """Modifie une commande"""
        print(f"🎯 Modification commande: {commande.numero_commande}")
        try:
            if not FORMULAIRES_AVAILABLE:
                self.show_error("Les formulaires ne sont pas disponibles")
                return

            app = MDApp.get_running_app()

            # Supprimer l'écran existant s'il y en a un
            if app.screen_manager.has_screen("commande_form"):
                app.screen_manager.remove_widget(app.screen_manager.get_screen("commande_form"))

            # Note: Le formulaire commande ne supporte que la création pour l'instant
            commande_form = CommandeFormScreen(callback=self.load_commandes)
            app.screen_manager.add_widget(commande_form)
            app.screen_manager.current = "commande_form"
            print("✅ Formulaire commande ouvert (mode création)")
        except Exception as e:
            print(f"❌ Erreur: {e}")
            self.show_error(f"Erreur: {e}")

    def show_error(self, message):
        """Affiche un message d'erreur"""
        from kivymd.uix.dialog import MDDialog
        from kivymd.uix.button import MDFlatButton

        dialog = MDDialog(
            title="Erreur",
            text=message,
            buttons=[
                MDFlatButton(
                    text="OK",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()

class GestionCommercialeCompleteApp(MDApp):
    """Application KivyMD complète avec formulaires"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = "Gestion Commerciale - KivyMD Complète"
        self.theme_cls.primary_palette = "Blue"
        self.theme_cls.theme_style = "Light"

    def build(self):
        """Construit l'interface principale"""
        # Initialiser la base de données
        try:
            self.db = DatabaseManager()
            print("✓ Base de données initialisée avec succès")
        except Exception as e:
            print(f"✗ Erreur lors de l'initialisation de la base de données: {e}")
            return MDLabel(text=f"Erreur: {e}")

        # Gestionnaire d'écrans
        self.screen_manager = MDScreenManager()

        # Ajouter les écrans principaux
        self.screen_manager.add_widget(DashboardScreen())
        self.screen_manager.add_widget(ClientsScreen())
        self.screen_manager.add_widget(ProduitsScreen())
        self.screen_manager.add_widget(CommandesScreen())

        # Layout principal
        main_layout = MDBoxLayout(orientation="vertical")

        # Barre de navigation supérieure
        toolbar = MDTopAppBar(
            title="Gestion Commerciale",
            right_action_items=[["refresh", lambda x: self.refresh_current_screen()]]
        )
        main_layout.add_widget(toolbar)

        # Contenu principal
        main_layout.add_widget(self.screen_manager)

        # Navigation inférieure
        bottom_nav = self.create_bottom_navigation()
        main_layout.add_widget(bottom_nav)

        return main_layout

    def create_bottom_navigation(self):
        """Crée la navigation inférieure"""
        bottom_nav = MDBottomNavigation()

        # Onglets de navigation
        tabs = [
            ("dashboard", "Accueil", "view-dashboard"),
            ("clients", "Clients", "account-group"),
            ("produits", "Produits", "package-variant"),
            ("commandes", "Commandes", "cart")
        ]

        for screen_name, title, icon in tabs:
            tab = MDBottomNavigationItem(
                name=screen_name,
                text=title,
                icon=icon
            )
            bottom_nav.add_widget(tab)

        # Lier la navigation
        bottom_nav.bind(on_tab_switch=self.on_tab_switch)

        return bottom_nav

    def on_tab_switch(self, instance_tabs, instance_tab, instance_tab_label, tab_text):
        """Gère le changement d'onglet"""
        self.screen_manager.current = instance_tab.name
        print(f"Navigation vers: {instance_tab.name}")

    def refresh_current_screen(self):
        """Rafraîchit l'écran actuel"""
        current_screen = self.screen_manager.current_screen
        if hasattr(current_screen, 'load_clients'):
            current_screen.load_clients()
        elif hasattr(current_screen, 'load_produits'):
            current_screen.load_produits()
        elif hasattr(current_screen, 'load_commandes'):
            current_screen.load_commandes()
        elif hasattr(current_screen, 'refresh_dashboard'):
            current_screen.refresh_dashboard()

        self.show_dialog("Information", "Données actualisées")

    def show_dialog(self, title, text):
        """Affiche un dialog d'information"""
        dialog = MDDialog(
            title=title,
            text=text,
            buttons=[
                MDFlatButton(
                    text="OK",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()

def main():
    """Point d'entrée principal"""
    try:
        app = GestionCommercialeCompleteApp()
        app.run()
    except Exception as e:
        print(f"Erreur lors du démarrage de l'application: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
