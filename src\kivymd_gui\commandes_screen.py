#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Écran de gestion des commandes KivyMD
"""

from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.list import MD<PERSON>ist, ThreeLineAvatarIconListItem
from kivymd.uix.list import IconLeftWidget, IconRightWidget
from kivymd.uix.textfield import MDTextField
from kivymd.uix.button import MDIconButton
from kivymd.uix.chip import MDChip
from kivymd.uix.card import MDCard
from kivymd.uix.label import MDLabel
# from kivymd.uix.refreshlayout import MDSwipeToRefresh  # Non disponible dans cette version
from kivymd.uix.gridlayout import MDGridLayout

from kivy.metrics import dp
from datetime import datetime

from .base_screen import BaseScreen
from ..models.commande import Commande
from ..models.client import Client

class CommandesScreen(BaseScreen):
    """Écran de gestion des commandes avec interface moderne"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = "commandes"
        self.commandes_data = []
        self.filtered_commandes = []
        self.status_filter = "Tous"
        self.build_screen()
        self.load_commandes()
    
    def build_screen(self):
        """Construit l'écran des commandes"""
        # Layout principal
        main_layout = MDBoxLayout(orientation="vertical")
        main_layout.spacing = dp(10)
        main_layout.padding = dp(10)
        
        # Barre d'outils
        toolbar = self.create_toolbar(
            "Gestion des Commandes",
            actions=[
                {
                    'type': 'icon',
                    'icon': 'refresh',
                    'callback': self.refresh_data
                },
                {
                    'type': 'icon',
                    'icon': 'plus',
                    'callback': self.add_commande
                }
            ]
        )
        main_layout.add_widget(toolbar)
        
        # Barre de recherche
        self.search_field = MDTextField(
            hint_text="Rechercher une commande...",
            icon_left="magnify",
            size_hint_y=None,
            height=dp(56),
            on_text=self.on_search_text
        )
        main_layout.add_widget(self.search_field)
        
        # Filtres par statut
        filters_layout = self.create_status_filters()
        main_layout.add_widget(filters_layout)
        
        # Statistiques rapides
        stats_card = self.create_stats_card()
        main_layout.add_widget(stats_card)
        
        # Liste des commandes
        scroll = MDScrollView()
        self.commandes_list = MDList()
        scroll.add_widget(self.commandes_list)
        main_layout.add_widget(scroll)
        
        self.add_widget(main_layout)
    
    def create_status_filters(self):
        """Crée les filtres par statut"""
        scroll = MDScrollView(
            size_hint_y=None,
            height=dp(50),
            do_scroll_y=False
        )
        
        layout = MDBoxLayout(
            orientation="horizontal",
            spacing=dp(10),
            adaptive_width=True
        )
        
        # Chips pour chaque statut
        statuts = ["Tous", "En attente", "Confirmée", "Expédiée", "Livrée", "Annulée"]
        self.status_chips = {}
        
        for statut in statuts:
            chip = MDChip(
                text=statut,
                check=statut == "Tous",
                on_release=lambda x, s=statut: self.set_status_filter(s)
            )
            self.status_chips[statut] = chip
            layout.add_widget(chip)
        
        scroll.add_widget(layout)
        return scroll
    
    def create_stats_card(self):
        """Crée la carte des statistiques commandes"""
        card = MDCard(
            size_hint_y=None,
            height=dp(100),
            elevation=2,
            padding=dp(15),
            md_bg_color="white",
            radius=[8]
        )
        
        layout = MDGridLayout(cols=2, spacing=dp(10))
        
        # Nombre total de commandes
        self.total_commandes_label = MDLabel(
            text="Commandes: 0",
            theme_text_color="Primary",
            font_style="Subtitle1",
            bold=True
        )
        layout.add_widget(self.total_commandes_label)
        
        # Commandes en attente
        self.attente_commandes_label = MDLabel(
            text="En attente: 0",
            theme_text_color="Primary",
            font_style="Subtitle1",
            bold=True
        )
        layout.add_widget(self.attente_commandes_label)
        
        # Chiffre d'affaires
        self.ca_label = MDLabel(
            text="CA: 0€",
            theme_text_color="Primary",
            font_style="Subtitle1"
        )
        layout.add_widget(self.ca_label)
        
        # Commandes du jour
        self.jour_commandes_label = MDLabel(
            text="Aujourd'hui: 0",
            theme_text_color="Primary",
            font_style="Subtitle1"
        )
        layout.add_widget(self.jour_commandes_label)
        
        card.add_widget(layout)
        return card
    
    def load_commandes(self):
        """Charge la liste des commandes"""
        try:
            self.commandes_data = Commande.get_all()
            self.apply_filters()
            self.update_stats()
            
        except Exception as e:
            self.handle_error(e, "Chargement des commandes")
            self.commandes_data = []
            self.filtered_commandes = []
    
    def apply_filters(self):
        """Applique les filtres actifs"""
        self.filtered_commandes = self.commandes_data.copy()
        
        # Filtre par statut
        if self.status_filter != "Tous":
            self.filtered_commandes = [
                commande for commande in self.filtered_commandes
                if commande.statut == self.status_filter
            ]
        
        # Filtre de recherche
        search_text = self.search_field.text.lower().strip() if hasattr(self, 'search_field') else ""
        if search_text:
            self.filtered_commandes = [
                commande for commande in self.filtered_commandes
                if (search_text in commande.numero_commande.lower() or
                    self.client_matches_search(commande.client_id, search_text))
            ]
        
        self.update_commandes_display()
    
    def client_matches_search(self, client_id, search_text):
        """Vérifie si un client correspond à la recherche"""
        try:
            client = Client.get_by_id(client_id)
            if client:
                return (search_text in client.nom.lower() or
                        search_text in client.prenom.lower() or
                        (client.entreprise and search_text in client.entreprise.lower()))
        except:
            pass
        return False
    
    def update_commandes_display(self):
        """Met à jour l'affichage de la liste des commandes"""
        self.commandes_list.clear_widgets()
        
        if not self.filtered_commandes:
            # État vide
            message = f"Aucune commande {self.status_filter.lower()}" if self.status_filter != "Tous" else "Aucune commande trouvée"
            empty_state = self.create_empty_state(
                f"{message}\nAppuyez sur + pour créer une commande",
                "cart-plus"
            )
            self.commandes_list.add_widget(empty_state)
            return
        
        # Trier par date (plus récentes en premier)
        sorted_commandes = sorted(
            self.filtered_commandes,
            key=lambda c: c.date_commande if c.date_commande else datetime.min,
            reverse=True
        )
        
        for commande in sorted_commandes:
            # Créer l'item de liste
            item = self.create_commande_item(commande)
            self.commandes_list.add_widget(item)
    
    def create_commande_item(self, commande):
        """Crée un item de liste pour une commande"""
        # Récupérer le client
        try:
            client = Client.get_by_id(commande.client_id)
            client_nom = str(client) if client else "Client inconnu"
        except:
            client_nom = "Client inconnu"
        
        # Texte principal
        primary_text = commande.numero_commande
        
        # Texte secondaire
        secondary_text = client_nom
        
        # Texte tertiaire avec date et montant
        try:
            if isinstance(commande.date_commande, str):
                date_obj = datetime.fromisoformat(commande.date_commande.replace('Z', '+00:00'))
                date_str = date_obj.strftime("%d/%m/%Y")
            else:
                date_str = commande.date_commande.strftime("%d/%m/%Y") if commande.date_commande else ""
        except:
            date_str = "Date inconnue"
        
        tertiary_text = f"{commande.statut} • {commande.total_ttc:.2f}€ • {date_str}"
        
        # Couleur et icône selon le statut
        status_config = {
            "En attente": {"color": "orange", "icon": "clock-outline"},
            "Confirmée": {"color": "blue", "icon": "check-circle"},
            "Expédiée": {"color": "purple", "icon": "truck"},
            "Livrée": {"color": "green", "icon": "check-all"},
            "Annulée": {"color": "red", "icon": "close-circle"}
        }
        
        config = status_config.get(commande.statut, {"color": "grey", "icon": "help-circle"})
        
        # Icône de gauche
        left_icon = IconLeftWidget(
            icon=config["icon"],
            theme_icon_color="Custom",
            icon_color=config["color"]
        )
        
        # Icône de droite
        right_icon = IconRightWidget(
            icon="chevron-right",
            theme_icon_color="Custom",
            icon_color="grey"
        )
        
        # Item de liste
        item = ThreeLineAvatarIconListItem(
            text=primary_text,
            secondary_text=secondary_text,
            tertiary_text=tertiary_text,
            left_widget=left_icon,
            right_widget=right_icon,
            on_release=lambda x, c=commande: self.on_commande_tap(c)
        )
        
        return item
    
    def update_stats(self):
        """Met à jour les statistiques"""
        total = len(self.commandes_data)
        en_attente = len([c for c in self.commandes_data if c.statut == "En attente"])
        ca_total = sum(c.total_ttc for c in self.commandes_data if c.statut != "Annulée")
        
        # Commandes du jour
        aujourd_hui = datetime.now().date()
        commandes_jour = 0
        for commande in self.commandes_data:
            try:
                if isinstance(commande.date_commande, str):
                    date_obj = datetime.fromisoformat(commande.date_commande.replace('Z', '+00:00'))
                    commande_date = date_obj.date()
                else:
                    commande_date = commande.date_commande.date() if commande.date_commande else None
                
                if commande_date == aujourd_hui:
                    commandes_jour += 1
            except:
                pass
        
        self.total_commandes_label.text = f"Commandes: {total}"
        self.attente_commandes_label.text = f"En attente: {en_attente}"
        self.ca_label.text = f"CA: {ca_total:.2f}€"
        self.jour_commandes_label.text = f"Aujourd'hui: {commandes_jour}"
    
    def set_status_filter(self, statut):
        """Définit le filtre de statut"""
        # Décocher tous les chips
        for chip in self.status_chips.values():
            chip.check = False
        
        # Cocher le chip sélectionné
        self.status_chips[statut].check = True
        
        # Appliquer le filtre
        self.status_filter = statut
        self.apply_filters()
    
    def on_search_text(self, instance, text):
        """Gère la recherche de commandes"""
        self.apply_filters()
    
    def on_commande_tap(self, commande):
        """Gère le tap sur une commande"""
        self.show_commande_options(commande)
    
    def show_commande_options(self, commande):
        """Affiche les options pour une commande"""
        from kivymd.uix.dialog import MDDialog
        from kivymd.uix.button import MDFlatButton
        from kivymd.uix.list import OneLineListItem
        
        # Créer les options
        options = [
            ("Voir détails", lambda: self.view_commande_details(commande)),
            ("Changer statut", lambda: self.change_status(commande)),
        ]
        
        # Options conditionnelles selon le statut
        if commande.statut not in ["Livrée", "Annulée"]:
            options.append(("Modifier", lambda: self.edit_commande(commande)))
        
        if commande.statut == "Confirmée":
            options.append(("Créer facture", lambda: self.create_facture(commande)))
        
        # Créer la liste d'options
        options_list = MDList()
        for text, callback in options:
            item = OneLineListItem(
                text=text,
                on_release=lambda x, cb=callback: self.execute_option(cb)
            )
            options_list.add_widget(item)
        
        # Créer et afficher le dialog
        self.options_dialog = MDDialog(
            title=commande.numero_commande,
            type="custom",
            content_cls=options_list,
            buttons=[
                MDFlatButton(
                    text="FERMER",
                    on_release=lambda x: self.options_dialog.dismiss()
                )
            ]
        )
        self.options_dialog.open()
    
    def execute_option(self, callback):
        """Exécute une option et ferme le dialog"""
        if hasattr(self, 'options_dialog'):
            self.options_dialog.dismiss()
        callback()
    
    def view_commande_details(self, commande):
        """Affiche les détails d'une commande"""
        try:
            client = Client.get_by_id(commande.client_id)
            client_nom = str(client) if client else "Client inconnu"
        except:
            client_nom = "Client inconnu"
        
        try:
            if isinstance(commande.date_commande, str):
                date_obj = datetime.fromisoformat(commande.date_commande.replace('Z', '+00:00'))
                date_str = date_obj.strftime("%d/%m/%Y %H:%M")
            else:
                date_str = commande.date_commande.strftime("%d/%m/%Y %H:%M") if commande.date_commande else ""
        except:
            date_str = "Date inconnue"
        
        details_text = f"""
Numéro: {commande.numero_commande}
Client: {client_nom}
Date: {date_str}
Statut: {commande.statut}
Total HT: {commande.total_ht:.2f}€
TVA: {commande.tva}%
Total TTC: {commande.total_ttc:.2f}€
Notes: {commande.notes or 'Aucune'}
        """.strip()
        
        from kivymd.uix.dialog import MDDialog
        from kivymd.uix.button import MDFlatButton
        
        dialog = MDDialog(
            title="Détails de la commande",
            text=details_text,
            buttons=[
                MDFlatButton(
                    text="FERMER",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()
    
    def change_status(self, commande):
        """Change le statut d'une commande"""
        # TODO: Implémenter le changement de statut
        self.show_snackbar(f"Changement de statut pour {commande.numero_commande} en cours de développement")
    
    def edit_commande(self, commande):
        """Modifie une commande"""
        # TODO: Implémenter l'édition de commande
        self.show_snackbar(f"Édition de {commande.numero_commande} en cours de développement")
    
    def create_facture(self, commande):
        """Crée une facture à partir d'une commande"""
        # TODO: Implémenter la création de facture
        self.show_snackbar(f"Création de facture pour {commande.numero_commande} en cours de développement")
    
    def add_commande(self, *args):
        """Ajoute une nouvelle commande"""
        # TODO: Implémenter le formulaire d'ajout
        self.show_snackbar("Création de commande en cours de développement")
    
    def refresh_data(self, *args):
        """Rafraîchit les données des commandes"""
        try:
            self.load_commandes()
            self.show_success("Commandes actualisées")
        except Exception as e:
            self.handle_error(e, "Actualisation")
        finally:
            pass
