@echo off
echo ========================================
echo   GESTION COMMERCIALE - KIVYMD COMPLETE
echo   Formulaires Client, Produit, Commande
echo ========================================
echo.

cd /d "c:\Users\<USER>\Desktop\gui"

echo Verification des dependances...
python -c "import kivymd; print('✓ KivyMD disponible')" 2>nul
if errorlevel 1 (
    echo ✗ KivyMD non trouve. Installation...
    pip install kivymd
)

echo.
echo ========================================
echo   FONCTIONNALITES DISPONIBLES
echo ========================================
echo.
echo 📊 DASHBOARD INTERACTIF:
echo   • Cartes cliquables (Clients, Produits, Commandes, CA)
echo   • Bouton rafraichir fonctionnel
echo   • Statistiques en temps reel
echo.
echo 📋 FORMULAIRES COMPLETS:
echo   • Formulaire Client (creation/modification)
echo   • Formulaire Produit (gestion stock, prix)
echo   • Formulaire Commande (selection client/produits)
echo.
echo 🎯 NAVIGATION:
echo   • Dashboard vers formulaires
echo   • Onglets: Clients, Produits, Commandes
echo   • Retour automatique apres sauvegarde
echo.
echo ========================================
echo   INSTRUCTIONS D'UTILISATION
echo ========================================
echo.
echo 1. DASHBOARD:
echo    - Cliquez sur les cartes pour ouvrir les formulaires
echo    - Utilisez le bouton rafraichir (icone)
echo.
echo 2. ONGLETS:
echo    - Clients: Liste + bouton "Nouveau Client"
echo    - Produits: Liste + bouton "Nouveau Produit"
echo    - Commandes: Liste + bouton "Nouvelle Commande"
echo.
echo 3. FORMULAIRES:
echo    - Remplissez les champs obligatoires (*)
echo    - Utilisez "Enregistrer" pour sauvegarder
echo    - Utilisez "Annuler" ou fleche retour
echo.
echo ========================================
echo.

echo Lancement de l'application complete...
echo.

python app_complete_kivymd.py

echo.
echo Application fermee.
pause
