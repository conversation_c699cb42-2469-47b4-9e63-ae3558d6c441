@echo off
echo ========================================
echo   GESTION COMMERCIALE - KIVYMD
echo   Dashboard avec Formulaires Integres
echo ========================================
echo.

cd /d "c:\Users\<USER>\Desktop\gui"

echo Verification des dependances...
python -c "import kivymd; print('✓ KivyMD disponible')" 2>nul
if errorlevel 1 (
    echo ✗ KivyMD non trouve. Installation...
    pip install kivymd
)

echo.
echo Instructions d'utilisation:
echo.
echo 1. Cliquez sur les cartes du dashboard pour ouvrir les formulaires
echo 2. Utilisez les boutons "Actions Rapides" pour creer rapidement
echo 3. Testez la navigation entre dashboard et formulaires
echo 4. Verifiez les messages dans la console
echo.
echo Lancement de l'application...
echo.

python app_dashboard_formulaires.py

echo.
echo Application fermee.
pause
