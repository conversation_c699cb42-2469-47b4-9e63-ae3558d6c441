#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fenêtre de gestion des clients
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from src.models.client import Client

class ClientsWindow:
    """Fenêtre de gestion des clients"""
    
    def __init__(self, parent):
        self.window = tk.Toplevel(parent)
        self.window.title("Gestion des Clients")
        self.window.geometry("1000x700")
        self.window.transient(parent)
        self.window.grab_set()
        
        self.selected_client = None
        self.setup_ui()
        self.load_clients()
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        # Frame principal
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configuration du redimensionnement
        self.window.columnconfigure(0, weight=1)
        self.window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # Titre
        title_label = ttk.Label(main_frame, text="Gestion des Clients", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # Frame pour les boutons d'action
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N), padx=(0, 10))
        
        # Boutons d'action
        ttk.Button(buttons_frame, text="Nouveau Client", 
                  command=self.new_client).grid(row=0, column=0, pady=5, sticky=(tk.W, tk.E))
        ttk.Button(buttons_frame, text="Modifier", 
                  command=self.edit_client).grid(row=1, column=0, pady=5, sticky=(tk.W, tk.E))
        ttk.Button(buttons_frame, text="Supprimer", 
                  command=self.delete_client).grid(row=2, column=0, pady=5, sticky=(tk.W, tk.E))
        
        ttk.Separator(buttons_frame, orient=tk.HORIZONTAL).grid(row=3, column=0, pady=10, sticky=(tk.W, tk.E))
        
        # Recherche
        ttk.Label(buttons_frame, text="Rechercher:").grid(row=4, column=0, sticky=tk.W, pady=(0, 5))
        self.search_var = tk.StringVar()
        self.search_var.trace("w", self.on_search_change)
        search_entry = ttk.Entry(buttons_frame, textvariable=self.search_var, width=20)
        search_entry.grid(row=5, column=0, pady=(0, 5), sticky=(tk.W, tk.E))
        
        ttk.Button(buttons_frame, text="Actualiser", 
                  command=self.load_clients).grid(row=6, column=0, pady=10, sticky=(tk.W, tk.E))
        
        buttons_frame.columnconfigure(0, weight=1)
        
        # Frame pour la liste des clients
        list_frame = ttk.LabelFrame(main_frame, text="Liste des clients", padding="10")
        list_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        # Treeview pour afficher les clients
        columns = ("ID", "Nom", "Prénom", "Entreprise", "Email", "Téléphone", "Ville")
        self.tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)
        
        # Configuration des colonnes
        column_widths = {"ID": 50, "Nom": 100, "Prénom": 100, "Entreprise": 150, 
                        "Email": 200, "Téléphone": 120, "Ville": 100}
        
        for col in columns:
            self.tree.heading(col, text=col, command=lambda c=col: self.sort_treeview(c))
            self.tree.column(col, width=column_widths.get(col, 100))
        
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.tree.yview)
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.tree.configure(yscrollcommand=v_scrollbar.set)
        
        h_scrollbar = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        self.tree.configure(xscrollcommand=h_scrollbar.set)
        
        # Bind double-click pour édition
        self.tree.bind("<Double-1>", lambda e: self.edit_client())
        self.tree.bind("<<TreeviewSelect>>", self.on_select_client)
    
    def load_clients(self):
        """Charge la liste des clients"""
        try:
            # Vider le treeview
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # Charger les clients
            clients = Client.get_all()
            
            for client in clients:
                self.tree.insert("", tk.END, values=(
                    client.id,
                    client.nom,
                    client.prenom,
                    client.entreprise or "",
                    client.email or "",
                    client.telephone or "",
                    client.ville or ""
                ))
                
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des clients: {e}")
    
    def on_search_change(self, *args):
        """Appelé quand le texte de recherche change"""
        search_term = self.search_var.get().strip()
        
        if search_term:
            self.search_clients(search_term)
        else:
            self.load_clients()
    
    def search_clients(self, term):
        """Recherche des clients"""
        try:
            # Vider le treeview
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # Rechercher les clients
            clients = Client.search(term)
            
            for client in clients:
                self.tree.insert("", tk.END, values=(
                    client.id,
                    client.nom,
                    client.prenom,
                    client.entreprise or "",
                    client.email or "",
                    client.telephone or "",
                    client.ville or ""
                ))
                
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la recherche: {e}")
    
    def on_select_client(self, event):
        """Appelé quand un client est sélectionné"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            client_id = item['values'][0]
            self.selected_client = Client.get_by_id(client_id)
    
    def new_client(self):
        """Ouvre la fenêtre de création d'un nouveau client"""
        ClientFormWindow(self.window, callback=self.load_clients)
    
    def edit_client(self):
        """Ouvre la fenêtre d'édition du client sélectionné"""
        if not self.selected_client:
            messagebox.showwarning("Attention", "Veuillez sélectionner un client à modifier")
            return
        
        ClientFormWindow(self.window, client=self.selected_client, callback=self.load_clients)
    
    def delete_client(self):
        """Supprime le client sélectionné"""
        if not self.selected_client:
            messagebox.showwarning("Attention", "Veuillez sélectionner un client à supprimer")
            return
        
        if messagebox.askyesno("Confirmation", 
                              f"Êtes-vous sûr de vouloir supprimer le client {self.selected_client} ?"):
            try:
                self.selected_client.delete()
                self.load_clients()
                self.selected_client = None
                messagebox.showinfo("Succès", "Client supprimé avec succès")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la suppression: {e}")
    
    def sort_treeview(self, col):
        """Trie le treeview par colonne"""
        data = [(self.tree.set(child, col), child) for child in self.tree.get_children('')]
        data.sort()
        
        for index, (val, child) in enumerate(data):
            self.tree.move(child, '', index)


class ClientFormWindow:
    """Fenêtre de formulaire pour créer/modifier un client"""
    
    def __init__(self, parent, client=None, callback=None):
        self.window = tk.Toplevel(parent)
        self.window.title("Nouveau Client" if client is None else "Modifier Client")
        self.window.geometry("500x600")
        self.window.transient(parent)
        self.window.grab_set()
        
        self.client = client
        self.callback = callback
        
        self.setup_ui()
        if self.client:
            self.load_client_data()
    
    def setup_ui(self):
        """Configure l'interface utilisateur du formulaire"""
        # Frame principal
        main_frame = ttk.Frame(self.window, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configuration du redimensionnement
        self.window.columnconfigure(0, weight=1)
        self.window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Variables pour les champs
        self.vars = {
            'nom': tk.StringVar(),
            'prenom': tk.StringVar(),
            'entreprise': tk.StringVar(),
            'email': tk.StringVar(),
            'telephone': tk.StringVar(),
            'adresse': tk.StringVar(),
            'ville': tk.StringVar(),
            'code_postal': tk.StringVar(),
            'pays': tk.StringVar(value="France")
        }
        
        # Champs du formulaire
        fields = [
            ("Nom *:", 'nom'),
            ("Prénom *:", 'prenom'),
            ("Entreprise:", 'entreprise'),
            ("Email:", 'email'),
            ("Téléphone:", 'telephone'),
            ("Adresse:", 'adresse'),
            ("Ville:", 'ville'),
            ("Code postal:", 'code_postal'),
            ("Pays:", 'pays')
        ]
        
        for i, (label, var_name) in enumerate(fields):
            ttk.Label(main_frame, text=label).grid(row=i, column=0, sticky=tk.W, pady=5, padx=(0, 10))
            
            if var_name == 'adresse':
                # Zone de texte pour l'adresse
                entry = tk.Text(main_frame, height=3, width=30)
                entry.grid(row=i, column=1, sticky=(tk.W, tk.E), pady=5)
                self.adresse_text = entry
            else:
                entry = ttk.Entry(main_frame, textvariable=self.vars[var_name], width=30)
                entry.grid(row=i, column=1, sticky=(tk.W, tk.E), pady=5)
        
        # Note sur les champs obligatoires
        ttk.Label(main_frame, text="* Champs obligatoires", 
                 font=("Arial", 8), foreground="red").grid(row=len(fields), column=0, columnspan=2, pady=10)
        
        # Boutons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=len(fields)+1, column=0, columnspan=2, pady=20)
        
        ttk.Button(buttons_frame, text="Enregistrer", 
                  command=self.save_client).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(buttons_frame, text="Annuler", 
                  command=self.window.destroy).grid(row=0, column=1)
    
    def load_client_data(self):
        """Charge les données du client dans le formulaire"""
        if self.client:
            for field, var in self.vars.items():
                value = getattr(self.client, field, "")
                var.set(value or "")
            
            # Charger l'adresse dans le widget Text
            self.adresse_text.delete(1.0, tk.END)
            self.adresse_text.insert(1.0, self.client.adresse or "")
    
    def save_client(self):
        """Sauvegarde le client"""
        try:
            # Validation des champs obligatoires
            if not self.vars['nom'].get().strip() or not self.vars['prenom'].get().strip():
                messagebox.showerror("Erreur", "Le nom et le prénom sont obligatoires")
                return
            
            # Créer ou modifier le client
            if self.client is None:
                client = Client()
            else:
                client = self.client
            
            # Remplir les données
            for field, var in self.vars.items():
                setattr(client, field, var.get().strip())
            
            # Récupérer l'adresse du widget Text
            client.adresse = self.adresse_text.get(1.0, tk.END).strip()
            
            # Sauvegarder
            client.save()
            
            messagebox.showinfo("Succès", "Client enregistré avec succès")
            
            # Appeler le callback pour actualiser la liste
            if self.callback:
                self.callback()
            
            self.window.destroy()
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'enregistrement: {e}")
