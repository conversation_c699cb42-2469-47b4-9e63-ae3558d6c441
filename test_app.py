#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de test pour l'application de gestion commerciale
"""

import sys
import os

# Ajouter le répertoire src au path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_database():
    """Test de la base de données"""
    print("Test de la base de données...")
    try:
        from src.database.database import DatabaseManager
        db = DatabaseManager()
        print("✓ Base de données initialisée avec succès")
        return True
    except Exception as e:
        print(f"✗ Erreur base de données: {e}")
        return False

def test_models():
    """Test des modèles"""
    print("\nTest des modèles...")
    try:
        from src.models.client import Client
        from src.models.produit import Produit
        from src.models.commande import Commande
        from src.models.facture import Facture
        
        # Test Client
        clients = Client.get_all()
        print(f"✓ {len(clients)} clients chargés")
        
        # Test Produit
        produits = Produit.get_all()
        print(f"✓ {len(produits)} produits chargés")
        
        # Test Commande
        commandes = Commande.get_all()
        print(f"✓ {len(commandes)} commandes chargées")
        
        # Test Facture
        factures = Facture.get_all()
        print(f"✓ {len(factures)} factures chargées")
        
        return True
    except Exception as e:
        print(f"✗ Erreur modèles: {e}")
        return False

def test_rapports():
    """Test des rapports"""
    print("\nTest des rapports...")
    try:
        from src.utils.rapports import RapportGenerator
        from datetime import datetime, timedelta
        
        rg = RapportGenerator()
        
        # Test statistiques générales
        stats = rg.statistiques_generales()
        print(f"✓ Statistiques générales: {len(stats)} indicateurs")
        
        # Test rapport stock faible
        stock_rapport = rg.rapport_stock_faible()
        print(f"✓ Rapport stock: {len(stock_rapport['produits'])} produits en rupture")
        
        return True
    except Exception as e:
        print(f"✗ Erreur rapports: {e}")
        return False

def test_gui_imports():
    """Test des imports GUI"""
    print("\nTest des imports GUI...")
    try:
        import tkinter as tk
        print("✓ Tkinter disponible")
        
        from src.gui.main_window import MainWindow
        print("✓ MainWindow importée")
        
        from src.gui.clients_window import ClientsWindow
        print("✓ ClientsWindow importée")
        
        from src.gui.produits_window import ProduitsWindow
        print("✓ ProduitsWindow importée")
        
        from src.gui.commandes_window import CommandesWindow
        print("✓ CommandesWindow importée")
        
        return True
    except Exception as e:
        print(f"✗ Erreur GUI: {e}")
        return False

def test_create_sample_data():
    """Crée des données d'exemple supplémentaires"""
    print("\nCréation de données d'exemple...")
    try:
        from src.models.client import Client
        from src.models.produit import Produit
        from src.models.commande import Commande
        
        # Créer un client de test
        client_test = Client(
            nom="Test",
            prenom="Utilisateur",
            entreprise="Entreprise Test",
            email="<EMAIL>",
            telephone="***********.89",
            adresse="123 Rue de Test",
            ville="Paris",
            code_postal="75001"
        )
        client_test.save()
        print(f"✓ Client de test créé (ID: {client_test.id})")
        
        # Créer un produit de test
        produit_test = Produit(
            nom="Produit Test",
            description="Produit de démonstration",
            prix_unitaire=99.99,
            stock_actuel=50,
            stock_minimum=10,
            code_produit="TEST-001"
        )
        produit_test.save()
        print(f"✓ Produit de test créé (ID: {produit_test.id})")
        
        # Créer une commande de test
        commande_test = Commande(
            client_id=client_test.id,
            notes="Commande de test"
        )
        commande_test.ajouter_ligne(produit_test.id, 2, produit_test.prix_unitaire)
        commande_test.save()
        print(f"✓ Commande de test créée (ID: {commande_test.id})")
        
        return True
    except Exception as e:
        print(f"✗ Erreur création données: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("=== Test de l'Application de Gestion Commerciale ===\n")
    
    tests = [
        test_database,
        test_models,
        test_rapports,
        test_gui_imports,
        test_create_sample_data
    ]
    
    results = []
    for test in tests:
        results.append(test())
    
    print("\n=== Résultats des Tests ===")
    passed = sum(results)
    total = len(results)
    
    print(f"Tests réussis: {passed}/{total}")
    
    if passed == total:
        print("✓ Tous les tests sont passés ! L'application est prête.")
        print("\nVous pouvez maintenant lancer l'application avec:")
        print("  python main.py")
        print("ou")
        print("  start.bat")
    else:
        print("✗ Certains tests ont échoué. Vérifiez les erreurs ci-dessus.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
