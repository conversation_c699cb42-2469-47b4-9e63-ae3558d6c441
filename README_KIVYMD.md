# Application de Gestion Commerciale - Version KivyMD

Une version moderne de l'application de gestion commerciale avec interface Material Design utilisant KivyMD.

![KivyMD](https://img.shields.io/badge/KivyMD-Material_Design-blue.svg)
![Python](https://img.shields.io/badge/Python-3.7+-green.svg)
![Cross-Platform](https://img.shields.io/badge/Platform-Cross_Platform-orange.svg)

## ✨ Nouveautés KivyMD

### 🎨 Interface Material Design
- **Design moderne** conforme aux guidelines Material Design 3
- **Animations fluides** et transitions élégantes
- **Thème adaptatif** clair/sombre
- **Interface tactile** optimisée pour tous les écrans

### 📱 Multi-plateforme
- **Windows** - Interface desktop moderne
- **Android** - Application mobile native (avec buildozer)
- **Linux** - Support complet
- **macOS** - Compatible

### 🚀 Fonctionnalités Avancées
- **Navigation moderne** avec drawer et bottom navigation
- **Cartes Material** pour l'affichage des données
- **Formulaires adaptatifs** avec validation en temps réel
- **Snackbars** pour les notifications
- **Dialogs modernes** pour les confirmations
- **Chips** pour les filtres
- **Refresh** par glissement (swipe to refresh)

## 🛠️ Installation

### Prérequis
- Python 3.7 ou supérieur
- Kivy et KivyMD

### Installation Automatique
```bash
# Installer toutes les dépendances KivyMD
install_kivymd.bat

# Ou manuellement
pip install kivy kivymd
```

### Installation Manuelle
```bash
# Installer Kivy
pip install kivy[base]

# Installer KivyMD
pip install kivymd

# Dépendances optionnelles
pip install pillow reportlab matplotlib
```

## 🚀 Démarrage

### Méthode 1 : Script de démarrage
```bash
start_kivymd.bat
```

### Méthode 2 : Ligne de commande
```bash
python app_kivymd.py
```

### Méthode 3 : Test avant démarrage
```bash
python test_kivymd.py
```

## 📱 Interface Utilisateur

### Navigation Principale
- **Bottom Navigation** - Navigation rapide entre les modules principaux
- **Navigation Drawer** - Menu latéral avec toutes les options
- **Top App Bar** - Actions contextuelles et titre

### Modules Disponibles

#### 🏠 Tableau de Bord
- **Cartes de statistiques** avec icônes colorées
- **Alertes de stock** en temps réel
- **Activité récente** avec historique
- **Rafraîchissement automatique** toutes les 30 secondes

#### 👥 Gestion des Clients
- **Liste moderne** avec avatars et informations détaillées
- **Recherche en temps réel** dans tous les champs
- **Formulaires adaptatifs** avec validation
- **Actions contextuelles** (voir, modifier, désactiver)
- **Statistiques** clients actifs/total

#### 📦 Gestion des Produits
- **Affichage avec codes couleur** selon le stock
- **Filtres par rupture** avec chips
- **Cartes de statistiques** (stock total, valeur, ruptures)
- **Alertes visuelles** pour les stocks faibles
- **Formulaires complets** avec validation

#### 🛒 Gestion des Commandes
- **Filtres par statut** avec chips colorés
- **Codes couleur** selon l'état de la commande
- **Recherche** par numéro ou client
- **Statistiques** en temps réel
- **Actions contextuelles** selon le statut

### Codes Couleur

#### Statuts des Commandes
- 🟠 **Orange** - En attente
- 🔵 **Bleu** - Confirmée
- 🟣 **Violet** - Expédiée
- 🟢 **Vert** - Livrée
- 🔴 **Rouge** - Annulée

#### États des Produits
- 🟢 **Vert** - Stock normal
- 🟠 **Orange** - Stock faible
- 🔴 **Rouge** - Rupture de stock

## 🎯 Fonctionnalités Implémentées

### ✅ Complètement Fonctionnel
- [x] **Tableau de bord** avec statistiques temps réel
- [x] **Navigation moderne** Material Design
- [x] **Gestion des clients** avec formulaires
- [x] **Gestion des produits** avec alertes stock
- [x] **Visualisation des commandes** avec filtres
- [x] **Base de données** SQLite intégrée
- [x] **Thème Material Design** adaptatif

### 🚧 En Développement
- [ ] **Création de commandes** avec sélection produits
- [ ] **Module de facturation** avec génération PDF
- [ ] **Rapports graphiques** avec charts
- [ ] **Synchronisation cloud** optionnelle
- [ ] **Mode sombre** automatique
- [ ] **Export/Import** de données

## 🔧 Configuration

### Thème
L'application utilise le thème Material Design 3 avec :
- **Palette principale** : Bleu
- **Palette d'accent** : Ambre
- **Style** : Clair (extensible au sombre)

### Base de Données
- **SQLite** local dans le dossier `data/`
- **Schéma** identique à la version Tkinter
- **Migration** automatique des données existantes

## 📊 Comparaison Tkinter vs KivyMD

| Fonctionnalité | Tkinter | KivyMD |
|----------------|---------|---------|
| **Design** | Interface classique | Material Design moderne |
| **Animations** | Limitées | Fluides et élégantes |
| **Mobile** | Non | Oui (Android/iOS) |
| **Thèmes** | Basique | Material Design 3 |
| **Performance** | Bonne | Excellente |
| **Développement** | Rapide | Moderne |

## 🚀 Avantages KivyMD

### Pour les Utilisateurs
- **Interface moderne** et intuitive
- **Expérience tactile** optimisée
- **Animations fluides** pour une meilleure UX
- **Responsive design** s'adapte à tous les écrans
- **Notifications élégantes** avec snackbars

### Pour les Développeurs
- **Code plus maintenable** avec architecture moderne
- **Composants réutilisables** Material Design
- **Cross-platform** sans effort supplémentaire
- **Communauté active** KivyMD
- **Documentation riche** et exemples

## 🔧 Développement

### Structure du Code KivyMD
```
src/kivymd_gui/
├── main_app.py          # Application principale
├── base_screen.py       # Écran de base avec utilitaires
├── dashboard_screen.py  # Tableau de bord
├── clients_screen.py    # Gestion clients
├── produits_screen.py   # Gestion produits
├── commandes_screen.py  # Gestion commandes
└── forms.py            # Formulaires Material Design
```

### Ajout de Nouveaux Écrans
1. Hériter de `BaseScreen`
2. Implémenter `build_screen()`
3. Ajouter au `ScreenManager`
4. Configurer la navigation

### Personnalisation du Thème
```python
self.theme_cls.primary_palette = "Blue"
self.theme_cls.accent_palette = "Amber"
self.theme_cls.theme_style = "Light"  # ou "Dark"
```

## 📱 Déploiement Mobile

### Android (avec Buildozer)
```bash
# Installer buildozer
pip install buildozer

# Initialiser le projet
buildozer init

# Compiler l'APK
buildozer android debug
```

### Configuration Buildozer
Le fichier `buildozer.spec` est pré-configuré pour l'application.

## 🐛 Résolution de Problèmes

### KivyMD ne s'installe pas
```bash
# Mettre à jour pip
python -m pip install --upgrade pip

# Installer avec verbose
pip install -v kivymd
```

### L'application ne démarre pas
```bash
# Vérifier les dépendances
python test_kivymd.py

# Vérifier Kivy
python -c "import kivy; print(kivy.__version__)"
```

### Erreurs d'affichage
- Vérifier les drivers graphiques
- Tester avec `python test_simple_kivymd.py`
- Consulter les logs Kivy

## 📚 Ressources

- **[Documentation KivyMD](https://kivymd.readthedocs.io/)**
- **[Material Design Guidelines](https://material.io/design)**
- **[Kivy Documentation](https://kivy.org/doc/stable/)**
- **[Exemples KivyMD](https://github.com/kivymd/KivyMD)**

## 🤝 Migration depuis Tkinter

### Données
- **Aucune migration nécessaire** - même base SQLite
- **Modèles identiques** - réutilisation complète
- **Fonctionnalités préservées** - aucune perte

### Utilisation
- **Interface plus moderne** mais logique similaire
- **Navigation améliorée** avec Material Design
- **Nouvelles fonctionnalités** tactiles

---

**Version KivyMD** : 2.0  
**Compatible avec** : Version Tkinter 1.0  
**Développé avec** : Python 3, KivyMD, Material Design 3
