#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Application KivyMD finale avec formulaires fonctionnels
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent / "src"))

from kivymd.app import MDApp
from kivymd.uix.screenmanager import MDScreenManager
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.textfield import MDTextField
from kivymd.uix.button import MDRaisedButton, MDFlatButton, MDIconButton
from kivymd.uix.label import MDLabel
from kivymd.uix.card import MDCard
from kivymd.uix.dialog import MDDialog
from kivymd.uix.toolbar import MDTopAppBar

from kivy.metrics import dp
from kivy.clock import Clock

# Imports des modèles
try:
    from src.database.database import DatabaseManager
    from src.models.client import Client
    from src.models.produit import Produit
    from src.models.commande import Commande
    MODELS_AVAILABLE = True
except ImportError as e:
    print(f"⚠ Modèles non disponibles: {e}")
    MODELS_AVAILABLE = False

class SimpleClientFormScreen(MDScreen):
    """Formulaire client ultra-simplifié"""
    
    def __init__(self, client=None, callback=None, **kwargs):
        MDScreen.__init__(self, **kwargs)
        self.name = "client_form"
        self.client = client
        self.callback = callback
        print("🔧 SimpleClientFormScreen initialisé")
        self.build_simple_form()
    
    def build_simple_form(self):
        """Construit un formulaire ultra-simple"""
        print("🔨 Construction formulaire client simple...")
        
        # Layout principal simple
        layout = MDBoxLayout(orientation="vertical", padding=dp(20), spacing=dp(15))
        
        # Titre
        title = MDLabel(
            text="Nouveau Client",
            font_style="H5",
            theme_text_color="Primary",
            halign="center",
            size_hint_y=None,
            height=dp(50)
        )
        layout.add_widget(title)
        
        # Champs simples
        self.prenom_field = MDTextField(
            hint_text="Prénom *",
            size_hint_y=None,
            height=dp(50)
        )
        layout.add_widget(self.prenom_field)
        
        self.nom_field = MDTextField(
            hint_text="Nom *",
            size_hint_y=None,
            height=dp(50)
        )
        layout.add_widget(self.nom_field)
        
        self.email_field = MDTextField(
            hint_text="Email",
            size_hint_y=None,
            height=dp(50)
        )
        layout.add_widget(self.email_field)
        
        self.telephone_field = MDTextField(
            hint_text="Téléphone",
            size_hint_y=None,
            height=dp(50)
        )
        layout.add_widget(self.telephone_field)
        
        # Boutons
        buttons_layout = MDBoxLayout(
            orientation="horizontal",
            spacing=dp(10),
            size_hint_y=None,
            height=dp(50)
        )
        
        cancel_btn = MDFlatButton(
            text="Annuler",
            on_release=self.go_back
        )
        buttons_layout.add_widget(cancel_btn)
        
        save_btn = MDRaisedButton(
            text="Enregistrer",
            on_release=self.save_client
        )
        buttons_layout.add_widget(save_btn)
        
        layout.add_widget(buttons_layout)
        
        self.add_widget(layout)
        print("✅ Formulaire client simple construit")
    
    def save_client(self, *args):
        """Sauvegarde simple"""
        print("💾 Sauvegarde client simple...")
        
        if not self.prenom_field.text.strip() or not self.nom_field.text.strip():
            dialog = MDDialog(
                title="Erreur",
                text="Prénom et nom obligatoires",
                buttons=[MDFlatButton(text="OK", on_release=lambda x: dialog.dismiss())]
            )
            dialog.open()
            return
        
        print(f"✅ Client sauvegardé: {self.prenom_field.text} {self.nom_field.text}")
        
        dialog = MDDialog(
            title="Succès",
            text=f"Client {self.prenom_field.text} {self.nom_field.text} créé !",
            buttons=[MDFlatButton(text="OK", on_release=lambda x: dialog.dismiss())]
        )
        dialog.open()
        
        Clock.schedule_once(lambda dt: self.go_back(), 1.5)
    
    def go_back(self, *args):
        """Retour"""
        print("🔙 Retour depuis formulaire client simple")
        if self.callback:
            self.callback()
        app = MDApp.get_running_app()
        if hasattr(app, 'screen_manager'):
            app.screen_manager.current = "dashboard"

class SimpleProduitFormScreen(MDScreen):
    """Formulaire produit ultra-simplifié"""
    
    def __init__(self, produit=None, callback=None, **kwargs):
        MDScreen.__init__(self, **kwargs)
        self.name = "produit_form"
        self.produit = produit
        self.callback = callback
        print("🔧 SimpleProduitFormScreen initialisé")
        self.build_simple_form()
    
    def build_simple_form(self):
        """Construit un formulaire ultra-simple"""
        print("🔨 Construction formulaire produit simple...")
        
        # Layout principal simple
        layout = MDBoxLayout(orientation="vertical", padding=dp(20), spacing=dp(15))
        
        # Titre
        title = MDLabel(
            text="Nouveau Produit",
            font_style="H5",
            theme_text_color="Primary",
            halign="center",
            size_hint_y=None,
            height=dp(50)
        )
        layout.add_widget(title)
        
        # Champs simples
        self.nom_field = MDTextField(
            hint_text="Nom du produit *",
            size_hint_y=None,
            height=dp(50)
        )
        layout.add_widget(self.nom_field)
        
        self.prix_field = MDTextField(
            hint_text="Prix unitaire *",
            input_filter="float",
            size_hint_y=None,
            height=dp(50)
        )
        layout.add_widget(self.prix_field)
        
        self.stock_field = MDTextField(
            hint_text="Stock actuel *",
            input_filter="int",
            size_hint_y=None,
            height=dp(50)
        )
        layout.add_widget(self.stock_field)
        
        self.description_field = MDTextField(
            hint_text="Description",
            size_hint_y=None,
            height=dp(50)
        )
        layout.add_widget(self.description_field)
        
        # Boutons
        buttons_layout = MDBoxLayout(
            orientation="horizontal",
            spacing=dp(10),
            size_hint_y=None,
            height=dp(50)
        )
        
        cancel_btn = MDFlatButton(
            text="Annuler",
            on_release=self.go_back
        )
        buttons_layout.add_widget(cancel_btn)
        
        save_btn = MDRaisedButton(
            text="Enregistrer",
            on_release=self.save_produit
        )
        buttons_layout.add_widget(save_btn)
        
        layout.add_widget(buttons_layout)
        
        self.add_widget(layout)
        print("✅ Formulaire produit simple construit")
    
    def save_produit(self, *args):
        """Sauvegarde simple"""
        print("💾 Sauvegarde produit simple...")
        
        if not self.nom_field.text.strip() or not self.prix_field.text.strip() or not self.stock_field.text.strip():
            dialog = MDDialog(
                title="Erreur",
                text="Nom, prix et stock obligatoires",
                buttons=[MDFlatButton(text="OK", on_release=lambda x: dialog.dismiss())]
            )
            dialog.open()
            return
        
        print(f"✅ Produit sauvegardé: {self.nom_field.text} - {self.prix_field.text}€")
        
        dialog = MDDialog(
            title="Succès",
            text=f"Produit {self.nom_field.text} créé !",
            buttons=[MDFlatButton(text="OK", on_release=lambda x: dialog.dismiss())]
        )
        dialog.open()
        
        Clock.schedule_once(lambda dt: self.go_back(), 1.5)
    
    def go_back(self, *args):
        """Retour"""
        print("🔙 Retour depuis formulaire produit simple")
        if self.callback:
            self.callback()
        app = MDApp.get_running_app()
        if hasattr(app, 'screen_manager'):
            app.screen_manager.current = "dashboard"

class DashboardScreen(MDScreen):
    """Dashboard simplifié"""
    
    def __init__(self, **kwargs):
        MDScreen.__init__(self, **kwargs)
        self.name = "dashboard"
        self.build_screen()
    
    def build_screen(self):
        """Construit le dashboard"""
        layout = MDBoxLayout(orientation="vertical", spacing=dp(10), padding=dp(10))
        
        # Titre
        title = MDLabel(
            text="Dashboard - Formulaires Fonctionnels",
            theme_text_color="Primary",
            font_style="H4",
            halign="center",
            size_hint_y=None,
            height=dp(60)
        )
        layout.add_widget(title)
        
        # Espace flexible
        spacer = MDLabel(text="", size_hint_y=1)
        layout.add_widget(spacer)
        
        # Boutons d'actions
        actions_title = MDLabel(
            text="Actions Rapides",
            theme_text_color="Primary",
            font_style="H6",
            halign="center",
            size_hint_y=None,
            height=dp(40)
        )
        layout.add_widget(actions_title)
        
        # Grille de boutons
        buttons_grid = MDGridLayout(cols=2, spacing=dp(10), size_hint_y=None, height=dp(120))
        
        client_btn = MDRaisedButton(
            text="Nouveau Client",
            on_release=self.open_client_form
        )
        buttons_grid.add_widget(client_btn)
        
        produit_btn = MDRaisedButton(
            text="Nouveau Produit",
            on_release=self.open_produit_form
        )
        buttons_grid.add_widget(produit_btn)
        
        layout.add_widget(buttons_grid)
        
        self.add_widget(layout)
    
    def open_client_form(self, *args):
        """Ouvre le formulaire client"""
        print("🎯 Ouverture formulaire client simple...")
        try:
            app = MDApp.get_running_app()
            
            # Supprimer l'écran existant
            if app.screen_manager.has_screen("client_form"):
                app.screen_manager.remove_widget(app.screen_manager.get_screen("client_form"))
            
            # Créer et ajouter le nouveau formulaire
            client_form = SimpleClientFormScreen(callback=self.on_form_closed)
            app.screen_manager.add_widget(client_form)
            app.screen_manager.current = "client_form"
            
            print("✅ Formulaire client simple ouvert")
            
        except Exception as e:
            print(f"❌ Erreur: {e}")
            import traceback
            traceback.print_exc()
    
    def open_produit_form(self, *args):
        """Ouvre le formulaire produit"""
        print("🎯 Ouverture formulaire produit simple...")
        try:
            app = MDApp.get_running_app()
            
            # Supprimer l'écran existant
            if app.screen_manager.has_screen("produit_form"):
                app.screen_manager.remove_widget(app.screen_manager.get_screen("produit_form"))
            
            # Créer et ajouter le nouveau formulaire
            produit_form = SimpleProduitFormScreen(callback=self.on_form_closed)
            app.screen_manager.add_widget(produit_form)
            app.screen_manager.current = "produit_form"
            
            print("✅ Formulaire produit simple ouvert")
            
        except Exception as e:
            print(f"❌ Erreur: {e}")
            import traceback
            traceback.print_exc()
    
    def on_form_closed(self):
        """Callback quand un formulaire est fermé"""
        print("🔙 Retour au dashboard")

class FinalApp(MDApp):
    """Application finale fonctionnelle"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = "Gestion Commerciale - Formulaires Fonctionnels"
        self.theme_cls.primary_palette = "Blue"
        self.theme_cls.theme_style = "Light"
    
    def build(self):
        """Construit l'application"""
        print("🚀 Démarrage application finale...")
        
        # Gestionnaire d'écrans
        self.screen_manager = MDScreenManager()
        
        # Ajouter le dashboard
        dashboard = DashboardScreen()
        self.screen_manager.add_widget(dashboard)
        
        print("✅ Application finale prête")
        return self.screen_manager

def main():
    """Point d'entrée"""
    try:
        print("=" * 60)
        print("  APPLICATION FINALE - FORMULAIRES FONCTIONNELS")
        print("=" * 60)
        print()
        
        app = FinalApp()
        app.run()
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
