#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gestionnaire de base de données SQLite
"""

import sqlite3
import os
from datetime import datetime

class DatabaseManager:
    """Gestionnaire de la base de données SQLite"""
    
    def __init__(self, db_path="data/gestion_commerciale.db"):
        """Initialise le gestionnaire de base de données"""
        self.db_path = db_path
        self.ensure_data_directory()
        self.init_database()
    
    def ensure_data_directory(self):
        """S'assure que le répertoire data existe"""
        data_dir = os.path.dirname(self.db_path)
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)
    
    def get_connection(self):
        """Retourne une connexion à la base de données"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # Pour accéder aux colonnes par nom
        return conn
    
    def init_database(self):
        """Initialise la base de données avec les tables nécessaires"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # Table des clients
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS clients (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    nom TEXT NOT NULL,
                    prenom TEXT NOT NULL,
                    entreprise TEXT,
                    email TEXT,
                    telephone TEXT,
                    adresse TEXT,
                    ville TEXT,
                    code_postal TEXT,
                    pays TEXT DEFAULT 'France',
                    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    actif BOOLEAN DEFAULT 1
                )
            ''')
            
            # Table des catégories de produits
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS categories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    nom TEXT NOT NULL UNIQUE,
                    description TEXT,
                    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Table des produits
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS produits (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    nom TEXT NOT NULL,
                    description TEXT,
                    prix_unitaire REAL NOT NULL,
                    stock_actuel INTEGER DEFAULT 0,
                    stock_minimum INTEGER DEFAULT 0,
                    categorie_id INTEGER,
                    code_produit TEXT UNIQUE,
                    actif BOOLEAN DEFAULT 1,
                    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (categorie_id) REFERENCES categories (id)
                )
            ''')
            
            # Table des commandes
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS commandes (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    numero_commande TEXT UNIQUE NOT NULL,
                    client_id INTEGER NOT NULL,
                    date_commande TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    statut TEXT DEFAULT 'En attente',
                    total_ht REAL DEFAULT 0,
                    tva REAL DEFAULT 20,
                    total_ttc REAL DEFAULT 0,
                    notes TEXT,
                    FOREIGN KEY (client_id) REFERENCES clients (id)
                )
            ''')
            
            # Table des lignes de commande
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS lignes_commande (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    commande_id INTEGER NOT NULL,
                    produit_id INTEGER NOT NULL,
                    quantite INTEGER NOT NULL,
                    prix_unitaire REAL NOT NULL,
                    total_ligne REAL NOT NULL,
                    FOREIGN KEY (commande_id) REFERENCES commandes (id),
                    FOREIGN KEY (produit_id) REFERENCES produits (id)
                )
            ''')
            
            # Table des factures
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS factures (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    numero_facture TEXT UNIQUE NOT NULL,
                    commande_id INTEGER,
                    client_id INTEGER NOT NULL,
                    date_facture TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    date_echeance TIMESTAMP,
                    statut TEXT DEFAULT 'Non payée',
                    total_ht REAL NOT NULL,
                    tva REAL DEFAULT 20,
                    total_ttc REAL NOT NULL,
                    notes TEXT,
                    FOREIGN KEY (commande_id) REFERENCES commandes (id),
                    FOREIGN KEY (client_id) REFERENCES clients (id)
                )
            ''')
            
            # Table des lignes de facture
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS lignes_facture (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    facture_id INTEGER NOT NULL,
                    produit_id INTEGER NOT NULL,
                    quantite INTEGER NOT NULL,
                    prix_unitaire REAL NOT NULL,
                    total_ligne REAL NOT NULL,
                    FOREIGN KEY (facture_id) REFERENCES factures (id),
                    FOREIGN KEY (produit_id) REFERENCES produits (id)
                )
            ''')
            
            # Insérer des données de test si les tables sont vides
            self.insert_sample_data(cursor)
            
            conn.commit()
            print("Base de données initialisée avec succès")
            
        except Exception as e:
            print(f"Erreur lors de l'initialisation de la base de données: {e}")
            conn.rollback()
        finally:
            conn.close()
    
    def insert_sample_data(self, cursor):
        """Insère des données d'exemple si les tables sont vides"""
        # Vérifier si des données existent déjà
        cursor.execute("SELECT COUNT(*) FROM clients")
        if cursor.fetchone()[0] == 0:
            # Insérer des catégories d'exemple
            categories = [
                ("Électronique", "Produits électroniques et informatiques"),
                ("Mobilier", "Meubles et équipements de bureau"),
                ("Fournitures", "Fournitures de bureau et consommables")
            ]
            cursor.executemany("INSERT INTO categories (nom, description) VALUES (?, ?)", categories)
            
            # Insérer des clients d'exemple
            clients = [
                ("Dupont", "Jean", "SARL Dupont", "<EMAIL>", "***********.89", "123 Rue de la Paix", "Paris", "75001", "France"),
                ("Martin", "Marie", "Entreprise Martin", "<EMAIL>", "***********.32", "456 Avenue des Champs", "Lyon", "69000", "France")
            ]
            cursor.executemany("""INSERT INTO clients (nom, prenom, entreprise, email, telephone, adresse, ville, code_postal, pays) 
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)""", clients)
            
            # Insérer des produits d'exemple
            produits = [
                ("Ordinateur portable", "PC portable 15 pouces", 899.99, 10, 2, 1, "PC-001"),
                ("Souris sans fil", "Souris optique sans fil", 29.99, 50, 10, 1, "SOURIS-001"),
                ("Bureau en bois", "Bureau en chêne massif", 299.99, 5, 1, 2, "BUREAU-001"),
                ("Ramette papier A4", "500 feuilles papier blanc", 4.99, 100, 20, 3, "PAPIER-001")
            ]
            cursor.executemany("""INSERT INTO produits (nom, description, prix_unitaire, stock_actuel, stock_minimum, categorie_id, code_produit) 
                                VALUES (?, ?, ?, ?, ?, ?, ?)""", produits)
    
    def execute_query(self, query, params=None):
        """Exécute une requête et retourne les résultats"""
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            if query.strip().upper().startswith('SELECT'):
                results = cursor.fetchall()
                return results
            else:
                conn.commit()
                return cursor.lastrowid
        except Exception as e:
            conn.rollback()
            raise e
        finally:
            conn.close()
    
    def execute_many(self, query, params_list):
        """Exécute une requête avec plusieurs jeux de paramètres"""
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            cursor.executemany(query, params_list)
            conn.commit()
            return cursor.rowcount
        except Exception as e:
            conn.rollback()
            raise e
        finally:
            conn.close()
