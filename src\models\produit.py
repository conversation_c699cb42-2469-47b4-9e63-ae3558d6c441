#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Modèle Produit
"""

from datetime import datetime
from src.database.database import DatabaseManager

class Produit:
    """Modèle pour gérer les produits"""
    
    def __init__(self, id=None, nom="", description="", prix_unitaire=0.0,
                 stock_actuel=0, stock_minimum=0, categorie_id=None,
                 code_produit="", actif=True, date_creation=None):
        self.id = id
        self.nom = nom
        self.description = description
        self.prix_unitaire = prix_unitaire
        self.stock_actuel = stock_actuel
        self.stock_minimum = stock_minimum
        self.categorie_id = categorie_id
        self.code_produit = code_produit
        self.actif = actif
        self.date_creation = date_creation or datetime.now()
        self.db = DatabaseManager()
    
    def save(self):
        """Sauvegarde le produit en base de données"""
        if self.id is None:
            # Nouveau produit
            query = """INSERT INTO produits (nom, description, prix_unitaire, stock_actuel,
                      stock_minimum, categorie_id, code_produit, actif) 
                      VALUES (?, ?, ?, ?, ?, ?, ?, ?)"""
            params = (self.nom, self.description, self.prix_unitaire, self.stock_actuel,
                     self.stock_minimum, self.categorie_id, self.code_produit, self.actif)
            self.id = self.db.execute_query(query, params)
        else:
            # Mise à jour produit existant
            query = """UPDATE produits SET nom=?, description=?, prix_unitaire=?, 
                      stock_actuel=?, stock_minimum=?, categorie_id=?, code_produit=?, actif=? 
                      WHERE id=?"""
            params = (self.nom, self.description, self.prix_unitaire, self.stock_actuel,
                     self.stock_minimum, self.categorie_id, self.code_produit, self.actif, self.id)
            self.db.execute_query(query, params)
        return self.id
    
    def delete(self):
        """Supprime le produit (désactivation)"""
        if self.id:
            query = "UPDATE produits SET actif=0 WHERE id=?"
            self.db.execute_query(query, (self.id,))
            self.actif = False
    
    def ajuster_stock(self, quantite):
        """Ajuste le stock du produit"""
        self.stock_actuel += quantite
        if self.id:
            query = "UPDATE produits SET stock_actuel=? WHERE id=?"
            self.db.execute_query(query, (self.stock_actuel, self.id))
    
    def est_en_rupture(self):
        """Vérifie si le produit est en rupture de stock"""
        return self.stock_actuel <= self.stock_minimum
    
    @classmethod
    def get_by_id(cls, produit_id):
        """Récupère un produit par son ID"""
        db = DatabaseManager()
        query = "SELECT * FROM produits WHERE id=? AND actif=1"
        result = db.execute_query(query, (produit_id,))
        if result:
            row = result[0]
            return cls(
                id=row['id'], nom=row['nom'], description=row['description'],
                prix_unitaire=row['prix_unitaire'], stock_actuel=row['stock_actuel'],
                stock_minimum=row['stock_minimum'], categorie_id=row['categorie_id'],
                code_produit=row['code_produit'], actif=row['actif'],
                date_creation=row['date_creation']
            )
        return None
    
    @classmethod
    def get_by_code(cls, code_produit):
        """Récupère un produit par son code"""
        db = DatabaseManager()
        query = "SELECT * FROM produits WHERE code_produit=? AND actif=1"
        result = db.execute_query(query, (code_produit,))
        if result:
            row = result[0]
            return cls(
                id=row['id'], nom=row['nom'], description=row['description'],
                prix_unitaire=row['prix_unitaire'], stock_actuel=row['stock_actuel'],
                stock_minimum=row['stock_minimum'], categorie_id=row['categorie_id'],
                code_produit=row['code_produit'], actif=row['actif'],
                date_creation=row['date_creation']
            )
        return None
    
    @classmethod
    def get_all(cls, actifs_seulement=True):
        """Récupère tous les produits"""
        db = DatabaseManager()
        if actifs_seulement:
            query = """SELECT p.*, c.nom as categorie_nom 
                      FROM produits p 
                      LEFT JOIN categories c ON p.categorie_id = c.id 
                      WHERE p.actif=1 ORDER BY p.nom"""
        else:
            query = """SELECT p.*, c.nom as categorie_nom 
                      FROM produits p 
                      LEFT JOIN categories c ON p.categorie_id = c.id 
                      ORDER BY p.nom"""
        results = db.execute_query(query)
        
        produits = []
        for row in results:
            produit = cls(
                id=row['id'], nom=row['nom'], description=row['description'],
                prix_unitaire=row['prix_unitaire'], stock_actuel=row['stock_actuel'],
                stock_minimum=row['stock_minimum'], categorie_id=row['categorie_id'],
                code_produit=row['code_produit'], actif=row['actif'],
                date_creation=row['date_creation']
            )
            produits.append(produit)
        return produits
    
    @classmethod
    def get_produits_en_rupture(cls):
        """Récupère les produits en rupture de stock"""
        db = DatabaseManager()
        query = """SELECT * FROM produits 
                  WHERE stock_actuel <= stock_minimum AND actif=1 
                  ORDER BY nom"""
        results = db.execute_query(query)
        
        produits = []
        for row in results:
            produit = cls(
                id=row['id'], nom=row['nom'], description=row['description'],
                prix_unitaire=row['prix_unitaire'], stock_actuel=row['stock_actuel'],
                stock_minimum=row['stock_minimum'], categorie_id=row['categorie_id'],
                code_produit=row['code_produit'], actif=row['actif'],
                date_creation=row['date_creation']
            )
            produits.append(produit)
        return produits
    
    @classmethod
    def search(cls, terme_recherche):
        """Recherche des produits par nom, description ou code"""
        db = DatabaseManager()
        query = """SELECT * FROM produits 
                  WHERE (nom LIKE ? OR description LIKE ? OR code_produit LIKE ?) 
                  AND actif=1 
                  ORDER BY nom"""
        terme = f"%{terme_recherche}%"
        results = db.execute_query(query, (terme, terme, terme))
        
        produits = []
        for row in results:
            produit = cls(
                id=row['id'], nom=row['nom'], description=row['description'],
                prix_unitaire=row['prix_unitaire'], stock_actuel=row['stock_actuel'],
                stock_minimum=row['stock_minimum'], categorie_id=row['categorie_id'],
                code_produit=row['code_produit'], actif=row['actif'],
                date_creation=row['date_creation']
            )
            produits.append(produit)
        return produits
    
    def __str__(self):
        """Représentation textuelle du produit"""
        return f"{self.nom} ({self.code_produit}) - {self.prix_unitaire}€"
    
    def get_nom_avec_stock(self):
        """Retourne le nom avec l'information de stock"""
        statut_stock = " [RUPTURE]" if self.est_en_rupture() else ""
        return f"{self.nom} (Stock: {self.stock_actuel}){statut_stock}"
