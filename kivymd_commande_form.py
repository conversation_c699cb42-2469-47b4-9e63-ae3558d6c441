#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Formulaire de commande KivyMD
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent / "src"))

from kivymd.app import <PERSON><PERSON>pp
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.textfield import MDTextField
from kivymd.uix.button import MDRaisedButton, MDFlatButton, MDIconButton
from kivymd.uix.label import MDLabel
from kivymd.uix.card import MDCard
from kivymd.uix.dialog import MDDialog
from kivymd.uix.selectioncontrol import MDSwitch
from kivymd.uix.menu import MDDropdownMenu
from kivymd.uix.list import OneLineAvatarListItem, IconLeftWidget, TwoLineListItem, ThreeLineListItem
from kivymd.uix.toolbar import MDTopAppBar
from kivymd.uix.chip import MDChip

from kivy.metrics import dp
from kivy.clock import Clock
from datetime import datetime

# Imports des modèles
from src.models.client import Client
from src.models.produit import Produit
from src.models.commande import Commande, LigneCommande

class CommandeFormScreen(MDScreen):
    """Formulaire pour créer/modifier une commande"""
    
    def __init__(self, commande=None, callback=None, **kwargs):
        super().__init__(**kwargs)
        self.name = "commande_form"
        self.commande = commande
        self.callback = callback
        self.selected_client = None
        self.lignes_commande = []
        self.client_menu = None
        self.produit_menu = None
        self.build_form()
    
    def build_form(self):
        """Construit le formulaire commande"""
        # Layout principal
        main_layout = MDBoxLayout(orientation="vertical")
        
        # Barre de titre
        toolbar = MDTopAppBar(
            title="Nouvelle Commande" if not self.commande else "Modifier Commande",
            left_action_items=[["arrow-left", lambda x: self.go_back()]],
            right_action_items=[["content-save", lambda x: self.save_commande()]]
        )
        main_layout.add_widget(toolbar)
        
        # Contenu scrollable
        scroll = MDScrollView()
        content = MDBoxLayout(orientation="vertical", spacing=dp(15), padding=dp(20))
        content.bind(minimum_height=content.setter('height'))
        
        # Section client
        client_title = MDLabel(
            text="Sélection du client",
            font_style="H6",
            theme_text_color="Primary",
            size_hint_y=None,
            height=dp(40)
        )
        content.add_widget(client_title)
        
        # Bouton de sélection client
        self.client_button = MDRaisedButton(
            text="Sélectionner un client",
            icon="account",
            on_release=self.show_client_menu,
            size_hint_y=None,
            height=dp(50)
        )
        content.add_widget(self.client_button)
        
        # Informations commande
        commande_title = MDLabel(
            text="Informations commande",
            font_style="H6",
            theme_text_color="Primary",
            size_hint_y=None,
            height=dp(40)
        )
        content.add_widget(commande_title)
        
        self.numero_field = MDTextField(
            hint_text="Numéro de commande",
            helper_text="Généré automatiquement si vide",
            helper_text_mode="on_focus",
            icon_left="receipt"
        )
        content.add_widget(self.numero_field)
        
        # Statut de la commande
        statut_layout = MDBoxLayout(
            orientation="horizontal",
            spacing=dp(10),
            size_hint_y=None,
            height=dp(60)
        )
        
        statut_label = MDLabel(
            text="Statut:",
            theme_text_color="Primary",
            size_hint_x=0.3
        )
        statut_layout.add_widget(statut_label)
        
        self.statut_button = MDRaisedButton(
            text="En attente",
            on_release=self.show_statut_menu,
            size_hint_x=0.7
        )
        statut_layout.add_widget(self.statut_button)
        
        content.add_widget(statut_layout)
        
        # Section produits
        produits_title = MDLabel(
            text="Produits de la commande",
            font_style="H6",
            theme_text_color="Primary",
            size_hint_y=None,
            height=dp(40)
        )
        content.add_widget(produits_title)
        
        # Bouton d'ajout de produit
        add_produit_btn = MDRaisedButton(
            text="Ajouter un produit",
            icon="plus",
            on_release=self.show_produit_menu,
            size_hint_y=None,
            height=dp(50)
        )
        content.add_widget(add_produit_btn)
        
        # Liste des produits
        self.produits_card = MDCard(
            size_hint_y=None,
            height=dp(200),
            elevation=2,
            padding=dp(10)
        )
        
        self.produits_layout = MDBoxLayout(
            orientation="vertical",
            spacing=dp(5)
        )
        
        produits_scroll = MDScrollView()
        produits_scroll.add_widget(self.produits_layout)
        self.produits_card.add_widget(produits_scroll)
        
        content.add_widget(self.produits_card)
        
        # Totaux
        totaux_title = MDLabel(
            text="Totaux",
            font_style="H6",
            theme_text_color="Primary",
            size_hint_y=None,
            height=dp(40)
        )
        content.add_widget(totaux_title)
        
        self.total_ht_label = MDLabel(
            text="Total HT: 0.00 €",
            theme_text_color="Primary",
            font_style="Subtitle1"
        )
        content.add_widget(self.total_ht_label)
        
        self.total_tva_label = MDLabel(
            text="Total TVA: 0.00 €",
            theme_text_color="Primary",
            font_style="Subtitle1"
        )
        content.add_widget(self.total_tva_label)
        
        self.total_ttc_label = MDLabel(
            text="Total TTC: 0.00 €",
            theme_text_color="Primary",
            font_style="H6",
            bold=True
        )
        content.add_widget(self.total_ttc_label)
        
        # Boutons d'action
        buttons_layout = MDBoxLayout(
            orientation="horizontal",
            spacing=dp(10),
            size_hint_y=None,
            height=dp(60),
            padding=[0, dp(20), 0, 0]
        )
        
        cancel_btn = MDFlatButton(
            text="Annuler",
            on_release=self.go_back
        )
        buttons_layout.add_widget(cancel_btn)
        
        save_btn = MDRaisedButton(
            text="Enregistrer",
            on_release=self.save_commande
        )
        buttons_layout.add_widget(save_btn)
        
        content.add_widget(buttons_layout)
        
        # Remplir les champs si modification
        if self.commande:
            self.fill_form()
        
        scroll.add_widget(content)
        main_layout.add_widget(scroll)
        self.add_widget(main_layout)
        
        # Initialiser les menus
        self.init_menus()
    
    def init_menus(self):
        """Initialise les menus déroulants"""
        # Menu clients
        clients = Client.get_all()
        client_items = []
        for client in clients:
            if client.actif:
                item = {
                    "text": f"{client.prenom} {client.nom}",
                    "viewclass": "OneLineListItem",
                    "on_release": lambda x=client: self.select_client(x)
                }
                client_items.append(item)
        
        self.client_menu = MDDropdownMenu(
            caller=self.client_button,
            items=client_items,
            width_mult=4
        )
        
        # Menu statuts
        statuts = ["En attente", "Confirmée", "Expédiée", "Livrée", "Annulée"]
        statut_items = []
        for statut in statuts:
            item = {
                "text": statut,
                "viewclass": "OneLineListItem",
                "on_release": lambda x=statut: self.select_statut(x)
            }
            statut_items.append(item)
        
        self.statut_menu = MDDropdownMenu(
            caller=self.statut_button,
            items=statut_items,
            width_mult=3
        )
        
        # Menu produits
        produits = Produit.get_all()
        produit_items = []
        for produit in produits:
            if produit.actif and produit.stock_actuel > 0:
                item = {
                    "text": f"{produit.nom} - {produit.prix_unitaire:.2f}€",
                    "viewclass": "OneLineListItem",
                    "on_release": lambda x=produit: self.add_produit_to_commande(x)
                }
                produit_items.append(item)
        
        self.produit_menu = MDDropdownMenu(
            caller=None,  # Sera défini lors de l'appel
            items=produit_items,
            width_mult=4
        )
    
    def show_client_menu(self, *args):
        """Affiche le menu de sélection client"""
        self.client_menu.open()
    
    def show_statut_menu(self, *args):
        """Affiche le menu de sélection statut"""
        self.statut_menu.open()
    
    def show_produit_menu(self, button):
        """Affiche le menu de sélection produit"""
        self.produit_menu.caller = button
        self.produit_menu.open()
    
    def select_client(self, client):
        """Sélectionne un client"""
        self.selected_client = client
        self.client_button.text = f"{client.prenom} {client.nom}"
        self.client_menu.dismiss()
    
    def select_statut(self, statut):
        """Sélectionne un statut"""
        self.statut_button.text = statut
        self.statut_menu.dismiss()
    
    def add_produit_to_commande(self, produit):
        """Ajoute un produit à la commande"""
        self.produit_menu.dismiss()
        
        # Vérifier si le produit est déjà dans la commande
        for ligne in self.lignes_commande:
            if ligne['produit'].id == produit.id:
                ligne['quantite'] += 1
                self.update_produits_display()
                self.calculate_totals()
                return
        
        # Ajouter nouveau produit
        ligne = {
            'produit': produit,
            'quantite': 1,
            'prix_unitaire': produit.prix_unitaire
        }
        self.lignes_commande.append(ligne)
        self.update_produits_display()
        self.calculate_totals()

    def update_produits_display(self):
        """Met à jour l'affichage des produits"""
        self.produits_layout.clear_widgets()

        if not self.lignes_commande:
            no_products = MDLabel(
                text="Aucun produit ajouté",
                theme_text_color="Secondary",
                halign="center"
            )
            self.produits_layout.add_widget(no_products)
            return

        for i, ligne in enumerate(self.lignes_commande):
            produit = ligne['produit']
            quantite = ligne['quantite']
            prix_unitaire = ligne['prix_unitaire']
            total_ligne = quantite * prix_unitaire

            # Layout pour chaque ligne
            ligne_layout = MDBoxLayout(
                orientation="horizontal",
                spacing=dp(10),
                size_hint_y=None,
                height=dp(60),
                padding=[dp(5), dp(5), dp(5), dp(5)]
            )

            # Informations produit
            info_layout = MDBoxLayout(
                orientation="vertical",
                size_hint_x=0.6
            )

            nom_label = MDLabel(
                text=produit.nom,
                font_style="Subtitle1",
                theme_text_color="Primary"
            )
            info_layout.add_widget(nom_label)

            prix_label = MDLabel(
                text=f"{prix_unitaire:.2f}€ x {quantite} = {total_ligne:.2f}€",
                font_style="Caption",
                theme_text_color="Secondary"
            )
            info_layout.add_widget(prix_label)

            ligne_layout.add_widget(info_layout)

            # Contrôles quantité
            qty_layout = MDBoxLayout(
                orientation="horizontal",
                spacing=dp(5),
                size_hint_x=0.3
            )

            minus_btn = MDIconButton(
                icon="minus",
                size_hint=(None, None),
                size=(dp(30), dp(30)),
                on_release=lambda x, idx=i: self.decrease_quantity(idx)
            )
            qty_layout.add_widget(minus_btn)

            qty_label = MDLabel(
                text=str(quantite),
                halign="center",
                theme_text_color="Primary"
            )
            qty_layout.add_widget(qty_label)

            plus_btn = MDIconButton(
                icon="plus",
                size_hint=(None, None),
                size=(dp(30), dp(30)),
                on_release=lambda x, idx=i: self.increase_quantity(idx)
            )
            qty_layout.add_widget(plus_btn)

            ligne_layout.add_widget(qty_layout)

            # Bouton supprimer
            delete_btn = MDIconButton(
                icon="delete",
                size_hint_x=0.1,
                on_release=lambda x, idx=i: self.remove_ligne(idx)
            )
            ligne_layout.add_widget(delete_btn)

            self.produits_layout.add_widget(ligne_layout)

    def increase_quantity(self, index):
        """Augmente la quantité d'un produit"""
        if index < len(self.lignes_commande):
            produit = self.lignes_commande[index]['produit']
            if self.lignes_commande[index]['quantite'] < produit.stock_actuel:
                self.lignes_commande[index]['quantite'] += 1
                self.update_produits_display()
                self.calculate_totals()
            else:
                self.show_error(f"Stock insuffisant pour {produit.nom}")

    def decrease_quantity(self, index):
        """Diminue la quantité d'un produit"""
        if index < len(self.lignes_commande):
            if self.lignes_commande[index]['quantite'] > 1:
                self.lignes_commande[index]['quantite'] -= 1
                self.update_produits_display()
                self.calculate_totals()
            else:
                self.remove_ligne(index)

    def remove_ligne(self, index):
        """Supprime une ligne de commande"""
        if index < len(self.lignes_commande):
            self.lignes_commande.pop(index)
            self.update_produits_display()
            self.calculate_totals()

    def calculate_totals(self):
        """Calcule les totaux de la commande"""
        total_ht = 0
        total_tva = 0

        for ligne in self.lignes_commande:
            produit = ligne['produit']
            quantite = ligne['quantite']
            prix_unitaire = ligne['prix_unitaire']

            ligne_ht = quantite * prix_unitaire
            ligne_tva = ligne_ht * (produit.taux_tva / 100)

            total_ht += ligne_ht
            total_tva += ligne_tva

        total_ttc = total_ht + total_tva

        self.total_ht_label.text = f"Total HT: {total_ht:.2f} €"
        self.total_tva_label.text = f"Total TVA: {total_tva:.2f} €"
        self.total_ttc_label.text = f"Total TTC: {total_ttc:.2f} €"

    def fill_form(self):
        """Remplit le formulaire avec les données de la commande"""
        if not self.commande:
            return

        self.numero_field.text = self.commande.numero_commande or ""
        self.statut_button.text = self.commande.statut or "En attente"

        # Sélectionner le client
        if self.commande.client_id:
            client = Client.get_by_id(self.commande.client_id)
            if client:
                self.select_client(client)

        # Charger les lignes de commande
        lignes = LigneCommande.get_by_commande_id(self.commande.id)
        for ligne in lignes:
            produit = Produit.get_by_id(ligne.produit_id)
            if produit:
                ligne_data = {
                    'produit': produit,
                    'quantite': ligne.quantite,
                    'prix_unitaire': ligne.prix_unitaire
                }
                self.lignes_commande.append(ligne_data)

        self.update_produits_display()
        self.calculate_totals()

    def validate_form(self):
        """Valide le formulaire"""
        errors = []

        if not self.selected_client:
            errors.append("Veuillez sélectionner un client")

        if not self.lignes_commande:
            errors.append("Veuillez ajouter au moins un produit")

        # Vérifier les stocks
        for ligne in self.lignes_commande:
            produit = ligne['produit']
            quantite = ligne['quantite']
            if quantite > produit.stock_actuel:
                errors.append(f"Stock insuffisant pour {produit.nom}")

        return errors

    def save_commande(self, *args):
        """Sauvegarde la commande"""
        # Valider
        errors = self.validate_form()
        if errors:
            self.show_error("\n".join(errors))
            return

        try:
            # Calculer les totaux
            total_ht = sum(ligne['quantite'] * ligne['prix_unitaire'] for ligne in self.lignes_commande)
            total_tva = sum(ligne['quantite'] * ligne['prix_unitaire'] * (ligne['produit'].taux_tva / 100)
                           for ligne in self.lignes_commande)
            total_ttc = total_ht + total_tva

            if self.commande:
                # Modification
                self.commande.client_id = self.selected_client.id
                self.commande.numero_commande = self.numero_field.text.strip() or None
                self.commande.statut = self.statut_button.text
                self.commande.total_ht = total_ht
                self.commande.total_tva = total_tva
                self.commande.total_ttc = total_ttc
                self.commande.save()

                # Supprimer les anciennes lignes
                LigneCommande.delete_by_commande_id(self.commande.id)

                message = "Commande modifiée avec succès"
            else:
                # Création
                commande_data = {
                    'client_id': self.selected_client.id,
                    'numero_commande': self.numero_field.text.strip() or None,
                    'statut': self.statut_button.text,
                    'total_ht': total_ht,
                    'total_tva': total_tva,
                    'total_ttc': total_ttc,
                    'date_commande': datetime.now()
                }
                self.commande = Commande.create(**commande_data)
                message = "Commande créée avec succès"

            # Créer les lignes de commande
            for ligne in self.lignes_commande:
                ligne_data = {
                    'commande_id': self.commande.id,
                    'produit_id': ligne['produit'].id,
                    'quantite': ligne['quantite'],
                    'prix_unitaire': ligne['prix_unitaire']
                }
                LigneCommande.create(**ligne_data)

                # Mettre à jour le stock
                produit = ligne['produit']
                produit.stock_actuel -= ligne['quantite']
                produit.save()

            # Callback et retour
            if self.callback:
                self.callback()

            self.show_success(message)
            Clock.schedule_once(lambda dt: self.go_back(), 1.5)

        except Exception as e:
            self.show_error(f"Erreur lors de la sauvegarde: {e}")

    def show_error(self, message):
        """Affiche un message d'erreur"""
        dialog = MDDialog(
            title="Erreur",
            text=message,
            buttons=[
                MDFlatButton(
                    text="OK",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()

    def show_success(self, message):
        """Affiche un message de succès"""
        dialog = MDDialog(
            title="Succès",
            text=message,
            buttons=[
                MDFlatButton(
                    text="OK",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()

    def go_back(self, *args):
        """Retour à l'écran précédent"""
        if self.callback:
            self.callback()
        # Navigation vers l'écran commandes
        app = MDApp.get_running_app()
        if hasattr(app, 'screen_manager'):
            app.screen_manager.current = "commandes"
