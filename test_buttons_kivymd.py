#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test simple des boutons KivyMD
"""

from kivymd.app import MDApp
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDRaisedButton, MDIconButton
from kivymd.uix.label import MDLabel
from kivymd.uix.dialog import MDDialog
from kivymd.uix.button import MDFlatButton

from kivy.metrics import dp

class TestButtonsApp(MDApp):
    """Application de test des boutons"""
    
    def build(self):
        """Construit l'interface de test"""
        layout = MDBoxLayout(
            orientation="vertical",
            spacing=dp(20),
            padding=dp(20)
        )
        
        # Titre
        title = MDLabel(
            text="Test des Boutons KivyMD",
            font_style="H4",
            halign="center",
            size_hint_y=None,
            height=dp(60)
        )
        layout.add_widget(title)
        
        # Bouton 1
        btn1 = MDRaisedButton(
            text="Bouton Test 1",
            size_hint=(None, None),
            size=(dp(200), dp(50)),
            pos_hint={"center_x": 0.5},
            on_release=self.on_button1_click
        )
        layout.add_widget(btn1)
        
        # Bouton 2
        btn2 = MDRaisedButton(
            text="Bouton Test 2",
            size_hint=(None, None),
            size=(dp(200), dp(50)),
            pos_hint={"center_x": 0.5},
            on_release=self.on_button2_click
        )
        layout.add_widget(btn2)
        
        # Bouton icône
        icon_btn = MDIconButton(
            icon="refresh",
            size_hint=(None, None),
            size=(dp(50), dp(50)),
            pos_hint={"center_x": 0.5},
            on_release=self.on_icon_click
        )
        layout.add_widget(icon_btn)
        
        # Label de statut
        self.status_label = MDLabel(
            text="Cliquez sur un bouton pour tester",
            halign="center",
            theme_text_color="Secondary"
        )
        layout.add_widget(self.status_label)
        
        return layout
    
    def on_button1_click(self, *args):
        """Callback pour le bouton 1"""
        print("Bouton 1 cliqué !")
        self.status_label.text = "Bouton 1 cliqué !"
        self.show_dialog("Test", "Le bouton 1 fonctionne parfaitement !")
    
    def on_button2_click(self, *args):
        """Callback pour le bouton 2"""
        print("Bouton 2 cliqué !")
        self.status_label.text = "Bouton 2 cliqué !"
        self.show_dialog("Test", "Le bouton 2 fonctionne parfaitement !")
    
    def on_icon_click(self, *args):
        """Callback pour le bouton icône"""
        print("Bouton icône cliqué !")
        self.status_label.text = "Bouton icône cliqué !"
        self.show_dialog("Test", "Le bouton icône fonctionne parfaitement !")
    
    def show_dialog(self, title, text):
        """Affiche un dialog"""
        dialog = MDDialog(
            title=title,
            text=text,
            buttons=[
                MDFlatButton(
                    text="OK",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()

def main():
    """Point d'entrée"""
    app = TestButtonsApp()
    app.run()

if __name__ == "__main__":
    main()
