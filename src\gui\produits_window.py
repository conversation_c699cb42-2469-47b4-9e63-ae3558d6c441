#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fenêtre de gestion des produits
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from src.models.produit import Produit
from src.database.database import DatabaseManager

class ProduitsWindow:
    """Fenêtre de gestion des produits"""
    
    def __init__(self, parent):
        self.window = tk.Toplevel(parent)
        self.window.title("Gestion des Produits")
        self.window.geometry("1200x700")
        self.window.transient(parent)
        self.window.grab_set()
        
        self.selected_produit = None
        self.db = DatabaseManager()
        self.setup_ui()
        self.load_produits()
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        # Frame principal
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configuration du redimensionnement
        self.window.columnconfigure(0, weight=1)
        self.window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # Titre
        title_label = ttk.Label(main_frame, text="Gestion des Produits", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # Frame pour les boutons d'action
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N), padx=(0, 10))
        
        # Boutons d'action
        ttk.Button(buttons_frame, text="Nouveau Produit", 
                  command=self.new_produit).grid(row=0, column=0, pady=5, sticky=(tk.W, tk.E))
        ttk.Button(buttons_frame, text="Modifier", 
                  command=self.edit_produit).grid(row=1, column=0, pady=5, sticky=(tk.W, tk.E))
        ttk.Button(buttons_frame, text="Supprimer", 
                  command=self.delete_produit).grid(row=2, column=0, pady=5, sticky=(tk.W, tk.E))
        
        ttk.Separator(buttons_frame, orient=tk.HORIZONTAL).grid(row=3, column=0, pady=10, sticky=(tk.W, tk.E))
        
        # Ajustement de stock
        ttk.Label(buttons_frame, text="Ajuster Stock:", font=("Arial", 10, "bold")).grid(row=4, column=0, sticky=tk.W, pady=(0, 5))
        
        stock_frame = ttk.Frame(buttons_frame)
        stock_frame.grid(row=5, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        self.stock_var = tk.StringVar()
        ttk.Entry(stock_frame, textvariable=self.stock_var, width=10).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(stock_frame, text="Ajouter", command=self.add_stock).grid(row=0, column=1, padx=(0, 2))
        ttk.Button(stock_frame, text="Retirer", command=self.remove_stock).grid(row=0, column=2)
        
        ttk.Separator(buttons_frame, orient=tk.HORIZONTAL).grid(row=6, column=0, pady=10, sticky=(tk.W, tk.E))
        
        # Recherche
        ttk.Label(buttons_frame, text="Rechercher:").grid(row=7, column=0, sticky=tk.W, pady=(0, 5))
        self.search_var = tk.StringVar()
        self.search_var.trace("w", self.on_search_change)
        search_entry = ttk.Entry(buttons_frame, textvariable=self.search_var, width=20)
        search_entry.grid(row=8, column=0, pady=(0, 5), sticky=(tk.W, tk.E))
        
        # Filtres
        ttk.Label(buttons_frame, text="Filtres:", font=("Arial", 10, "bold")).grid(row=9, column=0, sticky=tk.W, pady=(10, 5))
        
        self.filter_rupture = tk.BooleanVar()
        ttk.Checkbutton(buttons_frame, text="Produits en rupture", 
                       variable=self.filter_rupture, 
                       command=self.apply_filters).grid(row=10, column=0, sticky=tk.W, pady=2)
        
        ttk.Button(buttons_frame, text="Actualiser", 
                  command=self.load_produits).grid(row=11, column=0, pady=10, sticky=(tk.W, tk.E))
        
        buttons_frame.columnconfigure(0, weight=1)
        
        # Frame pour la liste des produits
        list_frame = ttk.LabelFrame(main_frame, text="Liste des produits", padding="10")
        list_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        # Treeview pour afficher les produits
        columns = ("ID", "Code", "Nom", "Prix", "Stock", "Stock Min", "Statut")
        self.tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)
        
        # Configuration des colonnes
        column_widths = {"ID": 50, "Code": 100, "Nom": 200, "Prix": 80, 
                        "Stock": 80, "Stock Min": 80, "Statut": 100}
        
        for col in columns:
            self.tree.heading(col, text=col, command=lambda c=col: self.sort_treeview(c))
            self.tree.column(col, width=column_widths.get(col, 100))
        
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.tree.yview)
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.tree.configure(yscrollcommand=v_scrollbar.set)
        
        h_scrollbar = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        self.tree.configure(xscrollcommand=h_scrollbar.set)
        
        # Bind double-click pour édition
        self.tree.bind("<Double-1>", lambda e: self.edit_produit())
        self.tree.bind("<<TreeviewSelect>>", self.on_select_produit)
    
    def load_produits(self):
        """Charge la liste des produits"""
        try:
            # Vider le treeview
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # Charger les produits
            produits = Produit.get_all()
            
            for produit in produits:
                # Déterminer le statut
                if produit.est_en_rupture():
                    statut = "RUPTURE"
                    tags = ("rupture",)
                else:
                    statut = "OK"
                    tags = ()
                
                self.tree.insert("", tk.END, values=(
                    produit.id,
                    produit.code_produit or "",
                    produit.nom,
                    f"{produit.prix_unitaire:.2f}€",
                    produit.stock_actuel,
                    produit.stock_minimum,
                    statut
                ), tags=tags)
            
            # Configuration des tags pour colorer les lignes en rupture
            self.tree.tag_configure("rupture", background="#ffcccc")
                
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des produits: {e}")
    
    def on_search_change(self, *args):
        """Appelé quand le texte de recherche change"""
        search_term = self.search_var.get().strip()
        
        if search_term:
            self.search_produits(search_term)
        else:
            self.apply_filters()
    
    def search_produits(self, term):
        """Recherche des produits"""
        try:
            # Vider le treeview
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # Rechercher les produits
            produits = Produit.search(term)
            
            for produit in produits:
                # Appliquer le filtre rupture si activé
                if self.filter_rupture.get() and not produit.est_en_rupture():
                    continue
                
                # Déterminer le statut
                if produit.est_en_rupture():
                    statut = "RUPTURE"
                    tags = ("rupture",)
                else:
                    statut = "OK"
                    tags = ()
                
                self.tree.insert("", tk.END, values=(
                    produit.id,
                    produit.code_produit or "",
                    produit.nom,
                    f"{produit.prix_unitaire:.2f}€",
                    produit.stock_actuel,
                    produit.stock_minimum,
                    statut
                ), tags=tags)
            
            # Configuration des tags
            self.tree.tag_configure("rupture", background="#ffcccc")
                
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la recherche: {e}")
    
    def apply_filters(self):
        """Applique les filtres sélectionnés"""
        if self.filter_rupture.get():
            self.show_rupture_products()
        else:
            if not self.search_var.get().strip():
                self.load_produits()
            else:
                self.search_produits(self.search_var.get().strip())
    
    def show_rupture_products(self):
        """Affiche uniquement les produits en rupture"""
        try:
            # Vider le treeview
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # Charger les produits en rupture
            produits = Produit.get_produits_en_rupture()
            
            for produit in produits:
                self.tree.insert("", tk.END, values=(
                    produit.id,
                    produit.code_produit or "",
                    produit.nom,
                    f"{produit.prix_unitaire:.2f}€",
                    produit.stock_actuel,
                    produit.stock_minimum,
                    "RUPTURE"
                ), tags=("rupture",))
            
            # Configuration des tags
            self.tree.tag_configure("rupture", background="#ffcccc")
                
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des produits en rupture: {e}")
    
    def on_select_produit(self, event):
        """Appelé quand un produit est sélectionné"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            produit_id = item['values'][0]
            self.selected_produit = Produit.get_by_id(produit_id)
    
    def new_produit(self):
        """Ouvre la fenêtre de création d'un nouveau produit"""
        ProduitFormWindow(self.window, callback=self.load_produits)
    
    def edit_produit(self):
        """Ouvre la fenêtre d'édition du produit sélectionné"""
        if not self.selected_produit:
            messagebox.showwarning("Attention", "Veuillez sélectionner un produit à modifier")
            return
        
        ProduitFormWindow(self.window, produit=self.selected_produit, callback=self.load_produits)
    
    def delete_produit(self):
        """Supprime le produit sélectionné"""
        if not self.selected_produit:
            messagebox.showwarning("Attention", "Veuillez sélectionner un produit à supprimer")
            return
        
        if messagebox.askyesno("Confirmation", 
                              f"Êtes-vous sûr de vouloir supprimer le produit {self.selected_produit.nom} ?"):
            try:
                self.selected_produit.delete()
                self.load_produits()
                self.selected_produit = None
                messagebox.showinfo("Succès", "Produit supprimé avec succès")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la suppression: {e}")
    
    def add_stock(self):
        """Ajoute du stock au produit sélectionné"""
        self.adjust_stock(positive=True)
    
    def remove_stock(self):
        """Retire du stock au produit sélectionné"""
        self.adjust_stock(positive=False)
    
    def adjust_stock(self, positive=True):
        """Ajuste le stock du produit sélectionné"""
        if not self.selected_produit:
            messagebox.showwarning("Attention", "Veuillez sélectionner un produit")
            return
        
        try:
            quantite_str = self.stock_var.get().strip()
            if not quantite_str:
                messagebox.showwarning("Attention", "Veuillez saisir une quantité")
                return
            
            quantite = int(quantite_str)
            if not positive:
                quantite = -quantite
            
            # Vérifier que le stock ne devient pas négatif
            nouveau_stock = self.selected_produit.stock_actuel + quantite
            if nouveau_stock < 0:
                messagebox.showerror("Erreur", "Le stock ne peut pas être négatif")
                return
            
            self.selected_produit.ajuster_stock(quantite)
            self.stock_var.set("")
            self.load_produits()
            
            action = "ajouté" if positive else "retiré"
            messagebox.showinfo("Succès", f"Stock {action} avec succès")
            
        except ValueError:
            messagebox.showerror("Erreur", "Veuillez saisir un nombre valide")
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'ajustement du stock: {e}")
    
    def sort_treeview(self, col):
        """Trie le treeview par colonne"""
        data = [(self.tree.set(child, col), child) for child in self.tree.get_children('')]
        
        # Tri numérique pour certaines colonnes
        if col in ["ID", "Prix", "Stock", "Stock Min"]:
            try:
                data.sort(key=lambda x: float(x[0].replace('€', '').replace(',', '.')))
            except:
                data.sort()
        else:
            data.sort()
        
        for index, (val, child) in enumerate(data):
            self.tree.move(child, '', index)


class ProduitFormWindow:
    """Fenêtre de formulaire pour créer/modifier un produit"""

    def __init__(self, parent, produit=None, callback=None):
        self.window = tk.Toplevel(parent)
        self.window.title("Nouveau Produit" if produit is None else "Modifier Produit")
        self.window.geometry("500x700")
        self.window.transient(parent)
        self.window.grab_set()

        self.produit = produit
        self.callback = callback
        self.db = DatabaseManager()

        self.setup_ui()
        if self.produit:
            self.load_produit_data()

    def setup_ui(self):
        """Configure l'interface utilisateur du formulaire"""
        # Frame principal
        main_frame = ttk.Frame(self.window, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configuration du redimensionnement
        self.window.columnconfigure(0, weight=1)
        self.window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        # Variables pour les champs
        self.vars = {
            'nom': tk.StringVar(),
            'code_produit': tk.StringVar(),
            'prix_unitaire': tk.StringVar(),
            'stock_actuel': tk.StringVar(value="0"),
            'stock_minimum': tk.StringVar(value="0"),
            'categorie_id': tk.StringVar()
        }

        # Champs du formulaire
        row = 0

        # Nom
        ttk.Label(main_frame, text="Nom *:").grid(row=row, column=0, sticky=tk.W, pady=5, padx=(0, 10))
        ttk.Entry(main_frame, textvariable=self.vars['nom'], width=30).grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5)
        row += 1

        # Code produit
        ttk.Label(main_frame, text="Code produit:").grid(row=row, column=0, sticky=tk.W, pady=5, padx=(0, 10))
        ttk.Entry(main_frame, textvariable=self.vars['code_produit'], width=30).grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5)
        row += 1

        # Description
        ttk.Label(main_frame, text="Description:").grid(row=row, column=0, sticky=tk.W, pady=5, padx=(0, 10))
        self.description_text = tk.Text(main_frame, height=4, width=30)
        self.description_text.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5)
        row += 1

        # Prix unitaire
        ttk.Label(main_frame, text="Prix unitaire *:").grid(row=row, column=0, sticky=tk.W, pady=5, padx=(0, 10))
        prix_frame = ttk.Frame(main_frame)
        prix_frame.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5)
        ttk.Entry(prix_frame, textvariable=self.vars['prix_unitaire'], width=20).grid(row=0, column=0, sticky=tk.W)
        ttk.Label(prix_frame, text="€").grid(row=0, column=1, sticky=tk.W, padx=(5, 0))
        row += 1

        # Catégorie
        ttk.Label(main_frame, text="Catégorie:").grid(row=row, column=0, sticky=tk.W, pady=5, padx=(0, 10))
        self.categorie_combo = ttk.Combobox(main_frame, textvariable=self.vars['categorie_id'], width=27, state="readonly")
        self.categorie_combo.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5)
        self.load_categories()
        row += 1

        # Stock actuel
        ttk.Label(main_frame, text="Stock actuel:").grid(row=row, column=0, sticky=tk.W, pady=5, padx=(0, 10))
        ttk.Entry(main_frame, textvariable=self.vars['stock_actuel'], width=30).grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5)
        row += 1

        # Stock minimum
        ttk.Label(main_frame, text="Stock minimum:").grid(row=row, column=0, sticky=tk.W, pady=5, padx=(0, 10))
        ttk.Entry(main_frame, textvariable=self.vars['stock_minimum'], width=30).grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5)
        row += 1

        # Note sur les champs obligatoires
        ttk.Label(main_frame, text="* Champs obligatoires",
                 font=("Arial", 8), foreground="red").grid(row=row, column=0, columnspan=2, pady=10)
        row += 1

        # Boutons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=row, column=0, columnspan=2, pady=20)

        ttk.Button(buttons_frame, text="Enregistrer",
                  command=self.save_produit).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(buttons_frame, text="Annuler",
                  command=self.window.destroy).grid(row=0, column=1)

    def load_categories(self):
        """Charge les catégories dans le combobox"""
        try:
            query = "SELECT id, nom FROM categories ORDER BY nom"
            results = self.db.execute_query(query)

            categories = [("", "-- Aucune catégorie --")]
            for row in results:
                categories.append((str(row[0]), row[1]))

            self.categorie_combo['values'] = [cat[1] for cat in categories]
            self.categories_data = {cat[1]: cat[0] for cat in categories}

        except Exception as e:
            print(f"Erreur lors du chargement des catégories: {e}")

    def load_produit_data(self):
        """Charge les données du produit dans le formulaire"""
        if self.produit:
            for field, var in self.vars.items():
                if field == 'categorie_id':
                    # Gérer la catégorie spécialement
                    if self.produit.categorie_id:
                        # Trouver le nom de la catégorie
                        try:
                            query = "SELECT nom FROM categories WHERE id=?"
                            result = self.db.execute_query(query, (self.produit.categorie_id,))
                            if result:
                                cat_nom = result[0][0]
                                self.categorie_combo.set(cat_nom)
                        except:
                            pass
                else:
                    value = getattr(self.produit, field, "")
                    var.set(str(value) if value is not None else "")

            # Charger la description dans le widget Text
            self.description_text.delete(1.0, tk.END)
            self.description_text.insert(1.0, self.produit.description or "")

    def save_produit(self):
        """Sauvegarde le produit"""
        try:
            # Validation des champs obligatoires
            if not self.vars['nom'].get().strip():
                messagebox.showerror("Erreur", "Le nom est obligatoire")
                return

            try:
                prix = float(self.vars['prix_unitaire'].get().replace(',', '.'))
                if prix < 0:
                    raise ValueError()
            except ValueError:
                messagebox.showerror("Erreur", "Le prix unitaire doit être un nombre positif")
                return

            try:
                stock_actuel = int(self.vars['stock_actuel'].get())
                if stock_actuel < 0:
                    raise ValueError()
            except ValueError:
                messagebox.showerror("Erreur", "Le stock actuel doit être un nombre entier positif")
                return

            try:
                stock_minimum = int(self.vars['stock_minimum'].get())
                if stock_minimum < 0:
                    raise ValueError()
            except ValueError:
                messagebox.showerror("Erreur", "Le stock minimum doit être un nombre entier positif")
                return

            # Créer ou modifier le produit
            if self.produit is None:
                produit = Produit()
            else:
                produit = self.produit

            # Remplir les données
            produit.nom = self.vars['nom'].get().strip()
            produit.code_produit = self.vars['code_produit'].get().strip()
            produit.prix_unitaire = prix
            produit.stock_actuel = stock_actuel
            produit.stock_minimum = stock_minimum

            # Récupérer la description du widget Text
            produit.description = self.description_text.get(1.0, tk.END).strip()

            # Gérer la catégorie
            cat_nom = self.categorie_combo.get()
            if cat_nom and cat_nom in self.categories_data:
                cat_id = self.categories_data[cat_nom]
                produit.categorie_id = int(cat_id) if cat_id else None
            else:
                produit.categorie_id = None

            # Sauvegarder
            produit.save()

            messagebox.showinfo("Succès", "Produit enregistré avec succès")

            # Appeler le callback pour actualiser la liste
            if self.callback:
                self.callback()

            self.window.destroy()

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'enregistrement: {e}")
