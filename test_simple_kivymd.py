#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test simple pour KivyMD
"""

import sys
import os
from pathlib import Path

# Ajouter le répertoire src au path
sys.path.append(str(Path(__file__).parent / "src"))

def test_simple_app():
    """Test simple de création d'app KivyMD"""
    try:
        from kivymd.app import MDApp
        from kivymd.uix.label import MDLabel
        from kivymd.uix.boxlayout import MDBoxLayout
        
        class SimpleApp(MDApp):
            def build(self):
                layout = MDBoxLayout(orientation="vertical")
                label = MDLabel(
                    text="Test KivyMD",
                    halign="center"
                )
                layout.add_widget(label)
                return layout
        
        app = SimpleApp()
        print("✓ Application simple créée")
        return True
        
    except Exception as e:
        print(f"✗ Erreur: {e}")
        return False

def test_database():
    """Test de la base de données"""
    try:
        from src.database.database import DatabaseManager
        db = DatabaseManager()
        print("✓ Base de données OK")
        return True
    except Exception as e:
        print(f"✗ Erreur DB: {e}")
        return False

def main():
    print("=== Test Simple KivyMD ===")
    
    tests = [test_database, test_simple_app]
    results = [test() for test in tests]
    
    if all(results):
        print("✓ Tous les tests passent")
        print("Vous pouvez essayer de lancer: python main_kivymd.py")
    else:
        print("✗ Certains tests échouent")

if __name__ == "__main__":
    main()
